"use client"

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { SimplePageBuilderEditor } from '@/lib/page-builder/components/simple-page-builder-editor'
import { usePageBuilder } from '@/lib/page-builder/context'
import { PageData } from '@/lib/page-builder/types'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Save, Eye } from 'lucide-react'
import { toast } from 'sonner'
import { Skeleton } from '@/components/ui/skeleton'

export default function PageBuilderEditorPage() {
  const params = useParams()
  const router = useRouter()
  const { dispatch, state } = usePageBuilder()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [pageData, setPageData] = useState<PageData | null>(null)

  const pageId = params.id as string

  // Load page data
  useEffect(() => {
    const fetchPageData = async () => {
      if (!pageId || pageId === 'new') {
        // Initialize with a new page
        const newPage = {
          id: 'new',
          title: 'New Page',
          slug: 'new-page',
          description: '',
          status: 'draft' as const,
          type: 'custom' as const,
          blocks: [],
          settings: {
            title: 'New Page',
          }
        }
        setPageData(newPage)
        dispatch({ type: 'SET_PAGE', payload: newPage })
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const response = await fetch(`/api/e-commerce/pages/${pageId}`)
        const data = await response.json()

        if (data.success && data.data) {
          setPageData(data.data)
          // Load page into Page Builder context
          dispatch({ type: 'SET_PAGE', payload: data.data })
        } else {
          toast.error('Failed to load page')
          router.push('/admin/page-builder')
        }
      } catch (error) {
        console.error('Error loading page:', error)
        toast.error('Failed to load page')
        router.push('/admin/page-builder')
      } finally {
        setLoading(false)
      }
    }

    fetchPageData()
  }, [pageId, dispatch, router])

  // Handle save
  const handleSave = async () => {
    if (!pageData) return

    try {
      setSaving(true)
      dispatch({ type: 'SAVE_PAGE', payload: { saving: true } })

      const isNewPage = pageData.id === 'new'
      const url = isNewPage ? '/api/e-commerce/pages' : `/api/e-commerce/pages/${pageData.id}`
      const method = isNewPage ? 'POST' : 'PUT'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: state.page.title,
          slug: state.page.slug,
          description: state.page.description,
          status: state.page.status,
          type: state.page.type,
          blocks: state.page.blocks,
          settings: state.page.settings,
        }),
      })

      const data = await response.json()

      if (data.success) {
        dispatch({ type: 'SAVE_PAGE', payload: { saving: false } })

        // If it was a new page, redirect to the actual page ID
        if (isNewPage && data.data?.id) {
          toast.success('Page created successfully!')
          router.push(`/admin/system/pages/${data.data.id}`)
          return
        }

        toast.success('Page saved successfully!')
      } else {
        throw new Error(data.error || 'Failed to save page')
      }
    } catch (error) {
      console.error('Error saving page:', error)
      dispatch({ type: 'SAVE_PAGE', payload: { saving: false } })
      toast.error('Failed to save page')
    } finally {
      setSaving(false)
    }
  }

  // Handle preview
  const handlePreview = () => {
    if (pageData?.slug) {
      window.open(`/preview/${pageData.slug}`, '_blank')
    }
  }

  // Handle back to page list
  const handleBack = () => {
    router.push('/admin/page-builder')
  }

  if (loading) {
    return (
      <div className="h-full flex flex-col">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-9 w-24" />
            <div>
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-16" />
          </div>
        </div>
        
        {/* Content Skeleton */}
        <div className="flex-1 p-8">
          <div className="max-w-4xl mx-auto">
            <Skeleton className="h-64 w-full mb-4" />
            <Skeleton className="h-32 w-full mb-4" />
            <Skeleton className="h-48 w-full" />
          </div>
        </div>
      </div>
    )
  }

  if (pageId === 'new') {
    return (
      <div className="h-full flex flex-col">
        {/* Header for new page */}
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Pages
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Create New Page</h1>
              <p className="text-sm text-muted-foreground">
                Start building your new page
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={saving}
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>
        
        {/* Editor for new page */}
        <div className="flex-1 overflow-hidden">
          <SimplePageBuilderEditor className="h-full" />
        </div>
      </div>
    )
  }

  if (!pageData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Page not found</h2>
          <p className="text-muted-foreground mb-4">
            The page you're looking for doesn't exist.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Page Header */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>
          <div>
            <h1 className="text-lg font-semibold">{pageData.title}</h1>
            <p className="text-sm text-muted-foreground">
              Editing page • /{pageData.slug} • {state.hasUnsavedChanges ? 'Unsaved changes' : 'All changes saved'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreview}
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            disabled={saving || !state.hasUnsavedChanges}
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </Button>
        </div>
      </div>
      
      {/* Page Builder Editor */}
      <div className="flex-1 overflow-hidden">
        <SimplePageBuilderEditor className="h-full" />
      </div>
    </div>
  )
}
