"use client"

import React from 'react'
import { PageBuilderCanvas } from '@/lib/page-builder/components/page-builder-canvas'
import { usePageBuilder } from '@/lib/page-builder/context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Eye, Save } from 'lucide-react'

export default function PageBuilderTest() {
  const { state, addBlock, setPreviewMode } = usePageBuilder()

  const handleAddHeroBlock = () => {
    addBlock('hero')
  }

  const handleAddTextBlock = () => {
    addBlock('text')
  }

  const handleTogglePreview = () => {
    setPreviewMode(!state.isPreviewMode)
  }

  return (
    <div className="h-full flex flex-col">
      {/* Test Header */}
      <div className="flex items-center justify-between p-4 bg-white border-b">
        <div>
          <h1 className="text-lg font-semibold">Page Builder Test</h1>
          <p className="text-sm text-muted-foreground">
            Testing the integrated page builder system
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddHeroBlock}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Hero
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddTextBlock}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Text
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleTogglePreview}
          >
            <Eye className="h-4 w-4 mr-2" />
            {state.isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
        </div>
      </div>

      {/* Test Stats */}
      <div className="p-4 bg-muted/30">
        <div className="grid grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Total Blocks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{state.page.blocks.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Selected Block</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                {state.selectedBlockId ? (
                  <span className="text-blue-600">
                    {state.page.blocks.find(b => b.id === state.selectedBlockId)?.type || 'Unknown'}
                  </span>
                ) : (
                  <span className="text-muted-foreground">None</span>
                )}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Preview Mode</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                {state.isPreviewMode ? (
                  <span className="text-green-600">Enabled</span>
                ) : (
                  <span className="text-orange-600">Disabled</span>
                )}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Device Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm capitalize">{state.devicePreview}</div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Canvas Area */}
      <div className="flex-1 overflow-hidden">
        <PageBuilderCanvas
          devicePreview={state.devicePreview}
          isPreviewMode={state.isPreviewMode}
        />
      </div>

      {/* Debug Info */}
      {!state.isPreviewMode && (
        <div className="p-4 bg-gray-50 border-t">
          <details className="text-xs">
            <summary className="cursor-pointer font-medium mb-2">Debug Info</summary>
            <pre className="bg-white p-2 rounded border overflow-auto max-h-32">
              {JSON.stringify({
                blocksCount: state.page.blocks.length,
                selectedBlockId: state.selectedBlockId,
                isPreviewMode: state.isPreviewMode,
                devicePreview: state.devicePreview,
                hasUnsavedChanges: state.hasUnsavedChanges,
                blocks: state.page.blocks.map(b => ({
                  id: b.id,
                  type: b.type,
                  position: b.position
                }))
              }, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  )
}
