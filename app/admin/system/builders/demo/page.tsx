"use client"

import React from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Sparkles, 
  Layout, 
  FileText, 
  Layers,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'

export default function UnifiedBuilderDemo() {
  return (
    <div className="h-full flex flex-col">
      {/* Demo Canvas */}
      <div className="flex-1 overflow-auto p-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="flex aspect-square size-12 items-center justify-center rounded-lg bg-blue-600 text-white">
                <Sparkles className="size-6" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Unified CMS Builder</h1>
                <p className="text-muted-foreground">Enterprise-grade content management system</p>
              </div>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <Badge variant="secondary">AI-Powered</Badge>
              <Badge variant="secondary">Drag & Drop</Badge>
              <Badge variant="secondary">Responsive</Badge>
              <Badge variant="secondary">TypeScript</Badge>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-pink-600" />
                  <span>Page Builder</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Create stunning pages with our visual page builder. Drag and drop blocks, customize layouts, and publish instantly.
                </p>
                <Button size="sm" className="w-full">
                  Open Page Builder
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Layout className="h-5 w-5 text-purple-600" />
                  <span>Layout Builder</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Design responsive layouts with our advanced grid system. Perfect for creating reusable templates.
                </p>
                <Button size="sm" variant="outline" className="w-full">
                  Open Layout Builder
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-5 w-5 text-blue-600" />
                  <span>AI Assistant</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Generate content and layouts with AI. Simply describe what you want and watch it come to life.
                </p>
                <Button size="sm" variant="outline" className="w-full">
                  Try AI Generator
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Demo Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Layers className="h-5 w-5" />
                <span>Demo Content Block</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-2">Hero Section</h3>
                <p className="mb-4">This is a demo hero section created with the unified builder system.</p>
                <Button variant="secondary">
                  Call to Action
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Feature 1</h4>
                  <p className="text-sm text-muted-foreground">
                    Responsive design that works on all devices.
                  </p>
                </div>
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Feature 2</h4>
                  <p className="text-sm text-muted-foreground">
                    AI-powered content generation and optimization.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Device Preview Demo */}
          <Card>
            <CardHeader>
              <CardTitle>Responsive Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="flex items-center space-x-2 text-sm">
                  <Monitor className="h-4 w-4" />
                  <span>Desktop</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Tablet className="h-4 w-4" />
                  <span>Tablet</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Smartphone className="h-4 w-4" />
                  <span>Mobile</span>
                </div>
              </div>
              <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-8 text-center">
                <p className="text-muted-foreground">
                  Use the device preview buttons in the toolbar to see how your content looks on different screen sizes.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  1
                </div>
                <div>
                  <p className="font-medium">Use the Left Panel</p>
                  <p className="text-sm text-muted-foreground">Access blocks, AI tools, and structure view from the left sidebar.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  2
                </div>
                <div>
                  <p className="font-medium">Edit Properties</p>
                  <p className="text-sm text-muted-foreground">Select any element and use the right panel to customize its properties.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  3
                </div>
                <div>
                  <p className="font-medium">Preview & Save</p>
                  <p className="text-sm text-muted-foreground">Use the toolbar to preview your changes and save when ready.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
