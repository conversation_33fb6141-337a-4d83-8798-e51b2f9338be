"use client";

import { usePathname } from "next/navigation";
import { NotificationCenter } from "@/components/admin/notification-center";
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminBreadcrumb } from "@/components/admin/admin-breadcrumb";
import { AdminAuthProvider } from "@/components/admin/admin-auth-provider";
import { AdminLayoutProvider } from "@/providers/admin-layout-provider";
import { EditorLayout } from "@/components/admin/editor-layout";
import { UnifiedEditorLayout } from "@/components/admin/unified-editor-layout";
import {
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useAdminUI } from "@/stores/use-admin-ui";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { isEditorMode } = useAdminUI();

  // Check if we're on the login page
  const isLoginPage = pathname === "/admin/login";

  // Enhanced editor route detection
  const isPageBuilderRoute = pathname?.match(/^\/admin\/system\/pages\/[^\/]+$/);
  const isPageBuilderEditRoute = pathname?.match(/^\/admin\/system\/pages\/[^\/]+\/edit$/);
  const isLayoutBuilderRoute = pathname?.match(/^\/admin\/system\/layouts\/editor\/[^\/]+$/);
  const isUnifiedBuilderRoute = pathname?.match(/^\/admin\/system\/builders\/[^\/]+$/);
  const isAnyEditorRoute = isPageBuilderRoute || isPageBuilderEditRoute || isLayoutBuilderRoute || isUnifiedBuilderRoute;

  // For login page, render without any wrappers
  if (isLoginPage) {
    return <>{children}</>;
  }

  // For editor routes, use the unified editor layout
  if (isAnyEditorRoute) {
    return (
      <AdminAuthProvider>
        <AdminLayoutProvider>
          <UnifiedEditorLayout editorType={
            (isPageBuilderRoute || isPageBuilderEditRoute) ? 'page' :
            isLayoutBuilderRoute ? 'layout' :
            'unified'
          }>
            {children}
          </UnifiedEditorLayout>
        </AdminLayoutProvider>
      </AdminAuthProvider>
    );
  }

  // Main admin layout
  return (
    <AdminAuthProvider>
      <AdminLayoutProvider>
        <SidebarProvider>
          <AdminSidebar />
          <main className="flex flex-col flex-1 h-screen overflow-hidden">
            {isEditorMode ? (
              <EditorLayout
                leftPanel={
                  <div className="flex h-full flex-col">
                    <div className="flex-1 overflow-auto p-4">
                      Left Panel Content
                    </div>
                  </div>
                }
                rightPanel={
                  <div className="flex h-full flex-col">
                    <div className="flex-1 overflow-auto p-4">
                      Right Panel Content
                    </div>
                  </div>
                }
              >
                {children}
              </EditorLayout>
            ) : (
              <>
                <header className="flex h-16 shrink-0 items-center gap-2 border-b">
                  <div className="flex items-center gap-2 px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 h-4" />
                    <AdminBreadcrumb />
                  </div>
                  <div className="ml-auto flex items-center gap-2 px-4">
                    <NotificationCenter />
                    <div className="text-sm text-muted-foreground">
                      Coco Milk Kids Admin
                    </div>
                  </div>
                </header>
                <div className="flex-1 overflow-auto">
                  <div className="container py-6">{children}</div>
                </div>
              </>
            )}
          </main>
        </SidebarProvider>
      </AdminLayoutProvider>
    </AdminAuthProvider>
  );
}
