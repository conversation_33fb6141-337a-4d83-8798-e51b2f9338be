'use client'

import { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Upload,
  X,
  File,
  Image,
  Video,
  Music,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  FolderPlus,
  Settings
} from 'lucide-react'
import { cn, formatFileSize } from '@/lib/utils'
import { useMedia } from '@/hooks/use-media'
import { toast } from 'sonner'

interface FileWithPreview extends File {
  id: string
  preview?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
  progress?: number
}

export default function MediaUploadPage() {
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [metadata, setMetadata] = useState({
    folder: 'root',
    tags: '',
    description: ''
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { uploadFiles } = useMedia()

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="h-5 w-5 text-green-600" />
    if (file.type.startsWith('video/')) return <Video className="h-5 w-5 text-blue-600" />
    if (file.type.startsWith('audio/')) return <Music className="h-5 w-5 text-purple-600" />
    if (file.type.startsWith('application/') || file.type.startsWith('text/')) {
      return <FileText className="h-5 w-5 text-orange-600" />
    }
    return <File className="h-5 w-5 text-gray-600" />
  }

  const createFilePreview = (file: File): string | undefined => {
    if (file.type.startsWith('image/')) {
      return URL.createObjectURL(file)
    }
    return undefined
  }

  const addFiles = useCallback((newFiles: FileList | File[]) => {
    const fileArray = Array.from(newFiles)
    const filesWithPreview: FileWithPreview[] = fileArray.map(file => ({
      ...file,
      id: Math.random().toString(36).substr(2, 9),
      preview: createFilePreview(file),
      status: 'pending',
      progress: 0
    }))

    setFiles(prev => [...prev, ...filesWithPreview])
  }, [])

  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== fileId)
    })
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const droppedFiles = e.dataTransfer.files
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles)
    }
  }, [addFiles])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles) {
      addFiles(selectedFiles)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }, [addFiles])

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const handleUpload = async () => {
    if (files.length === 0) return

    try {
      setUploading(true)

      // Update all files to uploading status
      setFiles(prev => prev.map(file => ({ 
        ...file, 
        status: 'uploading' as const,
        progress: 0
      })))

      const filesToUpload = files.map(f => new File([f], f.name, { type: f.type }))
      
      const uploadMetadata = {
        folder: metadata.folder || 'root',
        description: metadata.description,
        tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : []
      }

      // Simulate individual file progress
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(file => {
          if (file.status === 'uploading') {
            const newProgress = Math.min((file.progress || 0) + Math.random() * 15, 95)
            return { ...file, progress: newProgress }
          }
          return file
        }))
      }, 500)

      const uploadedFiles = await uploadFiles(filesToUpload, uploadMetadata)

      clearInterval(progressInterval)

      // Update file statuses
      setFiles(prev => prev.map(file => ({
        ...file,
        status: 'success' as const,
        progress: 100
      })))

      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`)

      // Clear files after successful upload
      setTimeout(() => {
        setFiles([])
        setMetadata({ folder: 'root', tags: '', description: '' })
      }, 2000)

    } catch (error) {
      console.error('Upload error:', error)
      setFiles(prev => prev.map(file => ({
        ...file,
        status: 'error' as const,
        error: error instanceof Error ? error.message : 'Upload failed'
      })))
      toast.error('Failed to upload files')
    } finally {
      setUploading(false)
    }
  }

  const clearAll = () => {
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
    setFiles([])
  }

  const retryFailed = () => {
    setFiles(prev => prev.map(file => 
      file.status === 'error' 
        ? { ...file, status: 'pending' as const, error: undefined }
        : file
    ))
  }

  const canUpload = files.length > 0 && !uploading
  const hasErrors = files.some(file => file.status === 'error')
  const successCount = files.filter(file => file.status === 'success').length
  const totalSize = files.reduce((sum, file) => sum + file.size, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Upload Files</h1>
          <p className="text-muted-foreground">
            Upload multiple files to your media library
          </p>
        </div>
      </div>

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle>File Upload</CardTitle>
          <CardDescription>
            Drag and drop files or click to browse
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Drop Zone */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
              dragOver ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400",
              uploading && "pointer-events-none opacity-50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={openFileDialog}
          >
            <div className="flex flex-col items-center space-y-4">
              <div className="p-4 bg-gray-100 rounded-full">
                <Upload className="h-8 w-8 text-gray-600" />
              </div>
              <div>
                <p className="text-lg font-medium">Drop files here or click to browse</p>
                <p className="text-sm text-muted-foreground">
                  Support for images, videos, audio, and documents up to 50MB each
                </p>
              </div>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={handleFileSelect}
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
          />

          {/* File Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="folder">Folder</Label>
              <Select
                value={metadata.folder}
                onValueChange={(value) => setMetadata(prev => ({ ...prev, folder: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="root">Root</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                  <SelectItem value="blog">Blog</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="documents">Documents</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                value={metadata.tags}
                onChange={(e) => setMetadata(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="tag1, tag2, tag3"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={metadata.description}
                onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Selected Files ({files.length})</CardTitle>
                <CardDescription>
                  Total size: {formatFileSize(totalSize)}
                  {successCount > 0 && ` • ${successCount} uploaded`}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                {hasErrors && (
                  <Button variant="outline" size="sm" onClick={retryFailed}>
                    <AlertCircle className="mr-2 h-4 w-4" />
                    Retry Failed
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={clearAll} disabled={uploading}>
                  Clear All
                </Button>
                <Button onClick={handleUpload} disabled={!canUpload}>
                  {uploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Upload {files.length} File{files.length !== 1 ? 's' : ''}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center space-x-3 p-3 border rounded-lg"
                >
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    <div className="w-12 h-12 flex items-center justify-center">
                      {getFileIcon(file)}
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                    
                    {file.status === 'uploading' && file.progress !== undefined && (
                      <div className="mt-1">
                        <Progress value={file.progress} className="h-1" />
                      </div>
                    )}
                    
                    {file.error && (
                      <p className="text-xs text-red-600 mt-1">{file.error}</p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {file.status === 'pending' && (
                      <Badge variant="outline">Pending</Badge>
                    )}
                    {file.status === 'uploading' && (
                      <Badge variant="secondary">
                        <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                        Uploading
                      </Badge>
                    )}
                    {file.status === 'success' && (
                      <Badge variant="default">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Success
                      </Badge>
                    )}
                    {file.status === 'error' && (
                      <Badge variant="destructive">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Error
                      </Badge>
                    )}

                    {!uploading && file.status !== 'success' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
