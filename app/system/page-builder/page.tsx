'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  Eye,
  Globe,
  File,
  RefreshCw,
  ArrowUpDown,
  Filter,
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { SkeletonTable } from '@/components/ui/skeleton-table'

interface Page {
  id: string
  title: string
  slug: string
  description: string
  status: 'draft' | 'published' | 'archived'
  type: 'system' | 'custom' | 'landing' | 'product' | 'category'
  updatedAt: string
  createdAt: string
  template?: string
}

export default function PageBuilderPage() {
  const router = useRouter()
  const [pages, setPages] = useState<Page[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [deletePageId, setDeletePageId] = useState<string | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [sortField, setSortField] = useState<'title' | 'updatedAt' | 'status'>('updatedAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [error, setError] = useState<string | null>(null)

  // Load pages
  useEffect(() => {
    loadPages()
  }, [])

  const loadPages = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/e-commerce/pages')
      const data = await response.json()

      if (data.success) {
        setPages(data.data)
      } else {
        setError(data.message || 'Failed to load pages')
        toast.error('Failed to load pages')
      }
    } catch (error) {
      console.error('Error loading pages:', error)
      setError('An unexpected error occurred')
      toast.error('Failed to load pages')
    } finally {
      setLoading(false)
    }
  }

  // Filter and sort pages
  const filteredAndSortedPages = pages
    .filter((page) => {
      const matchesSearch = 
        page.title.toLowerCase().includes(search.toLowerCase()) ||
        page.slug.toLowerCase().includes(search.toLowerCase())
      const matchesStatus = statusFilter === 'all' || page.status === statusFilter
      const matchesType = typeFilter === 'all' || page.type === typeFilter
      return matchesSearch && matchesStatus && matchesType
    })
    .sort((a, b) => {
      const aValue = sortField === 'updatedAt' ? new Date(a[sortField]).getTime() : a[sortField]
      const bValue = sortField === 'updatedAt' ? new Date(b[sortField]).getTime() : b[sortField]
      return (
        (aValue < bValue ? -1 : 1) * (sortDirection === 'asc' ? 1 : -1)
      )
    })

  const handleSort = (field: 'title' | 'updatedAt' | 'status') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleEdit = (pageId: string) => {
    router.push(`/admin/system/pages/${pageId}/edit`)
  }

  const handleDuplicate = async (pageId: string) => {
    try {
      const page = pages.find(p => p.id === pageId)
      if (!page) return

      const response = await fetch(`/api/e-commerce/pages/${pageId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: `${page.title} (Copy)`,
          status: 'draft'
        })
      })
      const data = await response.json()

      if (data.success) {
        toast.success('Page duplicated successfully')
        loadPages()
      } else {
        toast.error(data.message || 'Failed to duplicate page')
      }
    } catch (error) {
      console.error('Error duplicating page:', error)
      toast.error('Failed to duplicate page')
    }
  }

  const handleDelete = async () => {
    if (!deletePageId) return

    try {
      const response = await fetch(`/api/e-commerce/pages/${deletePageId}`, {
        method: 'DELETE',
      })
      const data = await response.json()

      if (data.success) {
        toast.success('Page deleted successfully')
        loadPages()
      } else {
        toast.error('Failed to delete page')
      }
    } catch (error) {
      console.error('Error deleting page:', error)
      toast.error('Failed to delete page')
    } finally {
      setDeletePageId(null)
    }
  }

  const handlePreview = (slug: string) => {
    window.open(`/preview/${slug}`, '_blank')
  }

  const handleVisit = (slug: string) => {
    window.open(`/${slug}`, '_blank')
  }

  return (
    <div className="container space-y-6 p-4 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pages</h1>
          <p className="text-muted-foreground">
            Create and manage your website pages using the page builder
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={loadPages} disabled={loading}>
            <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
            Refresh
          </Button>
          <Button onClick={() => router.push('/admin/system/pages/new/edit')}>
            <Plus className="mr-2 h-4 w-4" />
            New Page
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <Card className="p-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search pages..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="system">System</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
                <SelectItem value="landing">Landing</SelectItem>
                <SelectItem value="product">Product</SelectItem>
                <SelectItem value="category">Category</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </Card>

      {/* Error message */}
      {error && (
        <Card className="p-4 border-destructive">
          <div className="text-destructive">{error}</div>
        </Card>
      )}

      {/* Pages table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="cursor-pointer" onClick={() => handleSort('title')}>
                <div className="flex items-center">
                  Page
                  {sortField === 'title' && (
                    <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />
                  )}
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('status')}>
                <div className="flex items-center">
                  Status
                  {sortField === 'status' && (
                    <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />
                  )}
                </div>
              </TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('updatedAt')}>
                <div className="flex items-center">
                  Last Updated
                  {sortField === 'updatedAt' && (
                    <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />
                  )}
                </div>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <SkeletonTable columns={5} rows={5} />
            ) : filteredAndSortedPages.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  {search || statusFilter !== 'all' || typeFilter !== 'all' ? (
                    <div className="text-muted-foreground">
                      No pages found matching your filters.
                      <Button
                        variant="link"
                        onClick={() => {
                          setSearch('')
                          setStatusFilter('all')
                          setTypeFilter('all')
                        }}
                      >
                        Clear filters
                      </Button>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      No pages found. Create your first page to get started.
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedPages.map((page) => (
                <TableRow key={page.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium truncate max-w-[300px]">
                        {page.title}
                      </div>
                      <div className="text-sm text-muted-foreground truncate max-w-[300px]">
                        {page.slug}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      page.status === 'published' ? 'success' :
                      page.status === 'draft' ? 'secondary' : 'destructive'
                    }>
                      {page.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{page.type}</Badge>
                    {page.template && (
                      <Badge variant="outline" className="ml-2">
                        Template
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <span className="text-sm text-muted-foreground">
                            {formatDistanceToNow(new Date(page.updatedAt), { addSuffix: true })}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          {new Date(page.updatedAt).toLocaleString()}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEdit(page.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handlePreview(page.slug)}>
                          <Eye className="mr-2 h-4 w-4" />
                          Preview
                        </DropdownMenuItem>
                        {page.status === 'published' && (
                          <DropdownMenuItem onClick={() => handleVisit(page.slug)}>
                            <Globe className="mr-2 h-4 w-4" />
                            Visit Page
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleDuplicate(page.id)}>
                          <Copy className="mr-2 h-4 w-4" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => setDeletePageId(page.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Delete confirmation dialog */}
      <AlertDialog open={!!deletePageId} onOpenChange={() => setDeletePageId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the page
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={handleDelete}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
