"use client"

import React, { useState } from 'react'
import {
  ArrowLeft,
  Store,
  Plus,
  Layers,
  Sparkles,
  Settings,
  Package,
  Wand2,
  FileText,
  Layout
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { EditorType } from './unified-editor-layout'

// Import safe wrapper components
import { SafeBlockLibrary, SafeComponentTree } from './safe-page-builder-components'

interface EditorSidebarProps extends React.ComponentProps<typeof Sidebar> {
  editorType: EditorType
  onBackToAdmin?: () => void
}

export function EditorSidebar({ 
  editorType, 
  onBackToAdmin, 
  ...props 
}: EditorSidebarProps) {
  const [activeTab, setActiveTab] = useState<'blocks' | 'ai' | 'structure' | 'settings'>('blocks')

  // Get editor-specific configuration
  const getEditorConfig = () => {
    switch (editorType) {
      case 'page':
        return {
          title: 'Page Builder',
          subtitle: 'Visual Page Editor',
          icon: FileText,
          color: 'bg-pink-600',
          tabs: [
            { id: 'blocks', label: 'Blocks', icon: Plus },
            { id: 'ai', label: 'AI Assistant', icon: Sparkles },
            { id: 'structure', label: 'Structure', icon: Layers },
            { id: 'settings', label: 'Settings', icon: Settings }
          ]
        }
      case 'layout':
        return {
          title: 'Layout Builder',
          subtitle: 'Visual Layout Editor',
          icon: Layout,
          color: 'bg-purple-600',
          tabs: [
            { id: 'ai', label: 'AI Designer', icon: Wand2 },
            { id: 'blocks', label: 'Sections', icon: Package },
            { id: 'structure', label: 'Structure', icon: Layers },
            { id: 'settings', label: 'Settings', icon: Settings }
          ]
        }
      case 'unified':
        return {
          title: 'CMS Builder',
          subtitle: 'Unified Content Editor',
          icon: Store,
          color: 'bg-blue-600',
          tabs: [
            { id: 'ai', label: 'AI Generator', icon: Sparkles },
            { id: 'blocks', label: 'Blocks', icon: Plus },
            { id: 'structure', label: 'Structure', icon: Layers },
            { id: 'settings', label: 'Settings', icon: Settings }
          ]
        }
      default:
        return {
          title: 'Editor',
          subtitle: 'Visual Editor',
          icon: Store,
          color: 'bg-gray-600',
          tabs: []
        }
    }
  }

  const config = getEditorConfig()
  const IconComponent = config.icon

  // Safe component wrapper that handles context availability
  const SafePageBuilderComponent = ({ component }: { component: 'blocks' | 'structure' }) => {
    // Only render if we're actually in a page builder context
    if (editorType !== 'page') {
      return (
        <div className="p-4">
          <div className="text-center py-8 text-gray-400">
            <Layers className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Page Builder {component}</p>
            <p className="text-xs text-muted-foreground mt-1">
              Available when editing pages
            </p>
          </div>
        </div>
      )
    }

    // Use dynamic import to avoid context issues
    const [DynamicComponent, setDynamicComponent] = React.useState<React.ComponentType | null>(null)
    const [loading, setLoading] = React.useState(true)

    React.useEffect(() => {
      const loadComponent = async () => {
        try {
          if (component === 'blocks') {
            const module = await import('@/lib/page-builder/components/block-library')
            setDynamicComponent(() => module.BlockLibrary)
          } else if (component === 'structure') {
            const module = await import('@/lib/page-builder/components/component-tree')
            setDynamicComponent(() => module.ComponentTree)
          }
        } catch (error) {
          console.error('Failed to load component:', error)
          setDynamicComponent(null)
        } finally {
          setLoading(false)
        }
      }

      loadComponent()
    }, [component])

    if (loading) {
      return (
        <div className="p-4">
          <div className="text-center py-8 text-gray-400">
            <Layers className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Loading {component}...</p>
          </div>
        </div>
      )
    }

    if (!DynamicComponent) {
      return (
        <div className="p-4">
          <div className="text-center py-8 text-gray-400">
            <Layers className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Page Builder {component}</p>
            <p className="text-xs text-muted-foreground mt-1">
              Component not available
            </p>
          </div>
        </div>
      )
    }

    return <DynamicComponent />
  }

  // Safe layout builder component wrapper
  const SafeLayoutBuilderComponent = ({ component }: { component: 'ai' | 'blocks' }) => {
    // Only render if we're actually in a layout builder context
    if (editorType !== 'layout') {
      return (
        <div className="p-4">
          <div className="text-center py-8 text-gray-400">
            <Layout className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">Layout Builder {component}</p>
            <p className="text-xs text-muted-foreground mt-1">
              Available when editing layouts
            </p>
          </div>
        </div>
      )
    }

    // For now, return placeholder since layout builder is not fully implemented
    return (
      <div className="p-4">
        <div className="text-center py-8 text-gray-400">
          <Layout className="h-8 w-8 mx-auto mb-2" />
          <p className="text-sm">Layout Builder {component}</p>
          <p className="text-xs text-muted-foreground mt-1">
            Coming soon...
          </p>
        </div>
      </div>
    )
  }

  // Render tab content based on editor type and active tab
  const renderTabContent = () => {
    if (editorType === 'page') {
      switch (activeTab) {
        case 'blocks':
          return (
            <div className="h-full">
              <SafePageBuilderComponent component="blocks" />
            </div>
          )
        case 'ai':
          return (
            <div className="p-4">
              <div className="text-center py-8 text-gray-400">
                <Sparkles className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">AI Assistant for Page Building</p>
                <p className="text-xs text-muted-foreground mt-1">Coming soon...</p>
              </div>
            </div>
          )
        case 'structure':
          return (
            <div className="h-full">
              <SafePageBuilderComponent component="structure" />
            </div>
          )
        case 'settings':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                Page settings panel will be implemented here
              </div>
            </div>
          )
        default:
          return null
      }
    }

    if (editorType === 'layout') {
      switch (activeTab) {
        case 'ai':
          return <SafeLayoutBuilderComponent component="ai" />
        case 'blocks':
          return <SafeLayoutBuilderComponent component="blocks" />
        case 'structure':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                Layout structure tree will be displayed here
              </div>
            </div>
          )
        case 'settings':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                Layout settings panel will be implemented here
              </div>
            </div>
          )
        default:
          return null
      }
    }

    if (editorType === 'unified') {
      switch (activeTab) {
        case 'ai':
          return (
            <div className="p-4">
              <div className="text-center py-8 text-gray-400">
                <Wand2 className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">Unified AI Content Generator</p>
                <p className="text-xs text-muted-foreground mt-1">Advanced AI features</p>
              </div>
            </div>
          )
        case 'blocks':
          return <SafePageBuilderComponent component="blocks" />
        case 'structure':
          return <SafePageBuilderComponent component="structure" />
        case 'settings':
          return (
            <div className="p-4">
              <div className="text-sm text-muted-foreground">
                CMS builder settings panel
              </div>
            </div>
          )
        default:
          return null
      }
    }

    return null
  }

  return (
    <TooltipProvider>
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <div className="flex items-center">
                  <div className={`flex aspect-square size-8 items-center justify-center rounded-lg ${config.color} text-white`}>
                    <IconComponent className="size-4" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{config.title}</span>
                    <span className="truncate text-xs">{config.subtitle}</span>
                  </div>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
            {onBackToAdmin && (
              <SidebarMenuItem>
                <SidebarMenuButton size="sm" onClick={onBackToAdmin}>
                  <ArrowLeft className="h-4 w-4" />
                  <span className="truncate">Back to Admin</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )}
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent className="p-0">
          {/* Tab Selector */}
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <div className="grid grid-cols-2 gap-1 p-2">
                    {config.tabs.map((tab) => {
                      const TabIcon = tab.icon
                      return (
                        <Tooltip key={tab.id}>
                          <TooltipTrigger asChild>
                            <Button
                              variant={activeTab === tab.id ? 'default' : 'ghost'}
                              size="sm"
                              className="h-8 px-2"
                              onClick={() => setActiveTab(tab.id as any)}
                            >
                              <TabIcon className="h-3 w-3" />
                              <span className="ml-1 text-xs truncate">{tab.label}</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            {tab.label}
                          </TooltipContent>
                        </Tooltip>
                      )
                    })}
                  </div>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <Separator />

          {/* Content Area */}
          <SidebarGroup className="flex-1">
            <SidebarGroupLabel>
              {config.tabs.find(tab => tab.id === activeTab)?.label || 'Tools'}
            </SidebarGroupLabel>
            <SidebarGroupContent className="flex-1 overflow-hidden">
              <div className="h-full">
                {renderTabContent()}
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarRail />
      </Sidebar>
    </TooltipProvider>
  )
}
