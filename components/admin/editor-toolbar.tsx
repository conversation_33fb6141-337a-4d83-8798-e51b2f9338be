"use client"

import React from 'react'
import {
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  Save,
  Settings,
  MoreHorizontal,
  Sparkles,
  PanelLeftOpen,
  PanelLeftClose,
  PanelRightOpen,
  PanelRightClose,
  Play,
  RotateCcw,
  Download,
  Upload,
  Share
} from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useAdminUI } from '@/stores/use-admin-ui'
import { EditorType } from './unified-editor-layout'
import { EditorTypeSwitcher } from './editor-type-switcher'
import { cn } from '@/lib/utils'

interface EditorToolbarProps {
  editorType: EditorType
  className?: string
}

export function EditorToolbar({ editorType, className }: EditorToolbarProps) {
  const {
    editorLeftPanelOpen,
    editorRightPanelOpen,
    toggleEditorLeftPanel,
    toggleEditorRightPanel,
    editorHasUnsavedChanges,
    editorCanUndo,
    editorCanRedo,
    editorIsPreviewMode,
    editorDevicePreview,
    setEditorPreviewMode,
    setEditorDevicePreview
  } = useAdminUI()

  // Device preview options
  const deviceOptions = [
    { value: 'desktop' as const, icon: Monitor, label: 'Desktop', width: '100%' },
    { value: 'tablet' as const, icon: Tablet, label: 'Tablet', width: '768px' },
    { value: 'mobile' as const, icon: Smartphone, label: 'Mobile', width: '375px' },
  ]

  const handleSave = () => {
    // TODO: Implement save functionality based on editor type
    console.log('Saving...', editorType)
  }

  const handlePreview = () => {
    // TODO: Implement preview functionality
    console.log('Opening preview...', editorType)
  }

  const handleUndo = () => {
    // TODO: Implement undo functionality
    console.log('Undo')
  }

  const handleRedo = () => {
    // TODO: Implement redo functionality
    console.log('Redo')
  }

  const handleAIAssistant = () => {
    // TODO: Open AI assistant
    console.log('Opening AI assistant...')
  }

  return (
    <TooltipProvider>
      <div className={cn(
        "flex items-center justify-between h-14 px-4 bg-background border-b border-border",
        className
      )}>
        {/* Left Section - Panel Controls */}
        <div className="flex items-center space-x-2">
          {/* Editor Type Switcher */}
          <EditorTypeSwitcher />

          <Separator orientation="vertical" className="h-6" />

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleEditorLeftPanel}
                className={cn(
                  "h-8 w-8 p-0",
                  editorLeftPanelOpen && "bg-muted"
                )}
              >
                {editorLeftPanelOpen ? (
                  <PanelLeftClose className="h-4 w-4" />
                ) : (
                  <PanelLeftOpen className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {editorLeftPanelOpen ? 'Hide' : 'Show'} left panel
            </TooltipContent>
          </Tooltip>

          <Separator orientation="vertical" className="h-6" />

          {/* History Controls */}
          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleUndo}
                  disabled={!editorCanUndo}
                  className="h-8 w-8 p-0"
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Undo</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRedo}
                  disabled={!editorCanRedo}
                  className="h-8 w-8 p-0"
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Redo</TooltipContent>
            </Tooltip>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* AI Assistant */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAIAssistant}
                className="h-8 px-3"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                AI Assistant
              </Button>
            </TooltipTrigger>
            <TooltipContent>Open AI Assistant</TooltipContent>
          </Tooltip>
        </div>

        {/* Center Section - Device Preview */}
        <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
          {deviceOptions.map((device) => {
            const DeviceIcon = device.icon
            return (
              <Tooltip key={device.value}>
                <TooltipTrigger asChild>
                  <Button
                    variant={editorDevicePreview === device.value ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setEditorDevicePreview(device.value)}
                    className="h-7 w-7 p-0"
                  >
                    <DeviceIcon className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{device.label}</TooltipContent>
              </Tooltip>
            )
          })}
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center space-x-2">
          {/* Preview Mode Toggle */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={editorIsPreviewMode ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setEditorPreviewMode(!editorIsPreviewMode)}
                className="h-8 px-3"
              >
                {editorIsPreviewMode ? (
                  <>
                    <EyeOff className="h-4 w-4 mr-2" />
                    Exit Preview
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {editorIsPreviewMode ? 'Exit preview mode' : 'Enter preview mode'}
            </TooltipContent>
          </Tooltip>

          <Separator orientation="vertical" className="h-6" />

          {/* Save Button */}
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!editorHasUnsavedChanges}
            className="h-8 px-3"
          >
            <Save className="h-4 w-4 mr-2" />
            Save
            {editorHasUnsavedChanges && (
              <Badge variant="destructive" className="ml-2 h-4 px-1 text-xs">
                •
              </Badge>
            )}
          </Button>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handlePreview}>
                <Play className="h-4 w-4 mr-2" />
                Preview in New Tab
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share className="h-4 w-4 mr-2" />
                Share Preview
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Import
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset to Default
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Editor Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Separator orientation="vertical" className="h-6" />

          {/* Right Panel Toggle */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleEditorRightPanel}
                className={cn(
                  "h-8 w-8 p-0",
                  editorRightPanelOpen && "bg-muted"
                )}
              >
                {editorRightPanelOpen ? (
                  <PanelRightClose className="h-4 w-4" />
                ) : (
                  <PanelRightOpen className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {editorRightPanelOpen ? 'Hide' : 'Show'} properties panel
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  )
}
