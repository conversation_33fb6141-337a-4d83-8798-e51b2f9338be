"use client"

import React from 'react'
import { useRouter, usePathname } from 'next/navigation'
import {
  FileText,
  Layout,
  Store,
  ChevronDown
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { useAdminUI } from '@/stores/use-admin-ui'
import { EditorType } from './unified-editor-layout'

interface EditorTypeSwitcherProps {
  className?: string
}

export function EditorTypeSwitcher({ className }: EditorTypeSwitcherProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { currentEditorType } = useAdminUI()

  const editorTypes = [
    {
      type: 'page' as EditorType,
      name: 'Page Builder',
      description: 'Visual page editor with blocks',
      icon: FileText,
      color: 'bg-pink-600',
      route: '/admin/system/pages/new',
      badge: 'Popular'
    },
    {
      type: 'layout' as EditorType,
      name: 'Layout Builder',
      description: 'Advanced grid-based layouts',
      icon: Layout,
      color: 'bg-purple-600',
      route: '/admin/system/layouts/editor/new',
      badge: 'Advanced'
    },
    {
      type: 'unified' as EditorType,
      name: 'CMS Builder',
      description: 'Complete content management',
      icon: Store,
      color: 'bg-blue-600',
      route: '/admin/system/builders/demo',
      badge: 'Enterprise'
    }
  ]

  const currentEditor = editorTypes.find(editor => editor.type === currentEditorType)

  const handleEditorSwitch = (route: string) => {
    router.push(route)
  }

  if (!currentEditorType) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={`h-8 px-3 ${className}`}>
          {currentEditor && (
            <>
              <div className={`w-4 h-4 rounded ${currentEditor.color} mr-2`} />
              <span className="font-medium">{currentEditor.name}</span>
              <ChevronDown className="h-3 w-3 ml-2" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-80">
        <DropdownMenuLabel>Switch Editor Type</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {editorTypes.map((editor) => {
          const IconComponent = editor.icon
          const isActive = editor.type === currentEditorType
          
          return (
            <DropdownMenuItem
              key={editor.type}
              onClick={() => handleEditorSwitch(editor.route)}
              className={`p-3 cursor-pointer ${isActive ? 'bg-muted' : ''}`}
            >
              <div className="flex items-start space-x-3 w-full">
                <div className={`flex-shrink-0 w-8 h-8 rounded-lg ${editor.color} flex items-center justify-center text-white`}>
                  <IconComponent className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{editor.name}</span>
                    {editor.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {editor.badge}
                      </Badge>
                    )}
                    {isActive && (
                      <Badge variant="default" className="text-xs">
                        Active
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {editor.description}
                  </p>
                </div>
              </div>
            </DropdownMenuItem>
          )
        })}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => router.push('/admin')}
          className="p-3 cursor-pointer"
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-lg bg-gray-600 flex items-center justify-center text-white">
              <Store className="h-4 w-4" />
            </div>
            <div>
              <span className="font-medium">Back to Admin</span>
              <p className="text-sm text-muted-foreground">Return to main dashboard</p>
            </div>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
