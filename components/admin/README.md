# Admin Interface & Unified Editor Layout System

This directory contains the comprehensive admin interface components and the new unified editor layout system that adapts to different editor types.

## 🎯 Overview

The admin interface has been enhanced to seamlessly work with different editor layouts while maintaining a consistent user experience. The system automatically detects editor routes and switches between normal admin layout and specialized editor layouts.

## 🏗️ Architecture

### Core Components

#### 1. **UnifiedEditorLayout** (`unified-editor-layout.tsx`)
The main layout component that handles all editor types:
- **Page Builder**: Visual page editing with blocks and AI assistance
- **Layout Builder**: Advanced grid-based layout creation
- **Unified Builder**: Complete CMS content management

**Features:**
- Automatic editor type detection
- Resizable panels with constraints
- State persistence through Zustand
- Responsive design support

#### 2. **EditorSidebar** (`editor-sidebar.tsx`)
Adaptive sidebar that changes based on editor type:
- **Tabbed Interface**: Blocks, AI, Structure, Settings
- **Context-Aware Content**: Different tools for different editors
- **Integration**: Connects with existing builder components

#### 3. **EditorToolbar** (`editor-toolbar.tsx`)
Unified toolbar with common editor actions:
- **Device Preview**: Desktop, tablet, mobile switching
- **History Controls**: Undo/redo with state tracking
- **Panel Controls**: Show/hide left and right panels
- **AI Assistant**: Quick access to AI-powered features
- **Save/Preview**: Content management actions

#### 4. **EditorPropertiesPanel** (`editor-properties-panel.tsx`)
Comprehensive properties editor:
- **Collapsible Sections**: General, Styling, Layout, Responsive, Advanced
- **Dynamic Content**: Adapts to selected element type
- **Form Controls**: Complete form system for all properties

## 🔧 State Management

### Enhanced AdminUI Store (`stores/use-admin-ui.ts`)

Extended with editor-specific state:

```typescript
type EditorType = 'page' | 'layout' | 'unified' | null

interface AdminUIState {
  // Editor mode
  isEditorMode: boolean
  currentEditorType: EditorType
  editorRightPanelOpen: boolean
  editorLeftPanelOpen: boolean
  editorLeftPanelWidth: number
  editorRightPanelWidth: number
  
  // Editor state
  editorHasUnsavedChanges: boolean
  editorCanUndo: boolean
  editorCanRedo: boolean
  editorIsPreviewMode: boolean
  editorDevicePreview: 'desktop' | 'tablet' | 'mobile'
}
```

### Key Actions

- `setEditorMode(isEnabled, editorType)` - Enter/exit editor mode
- `setEditorPreviewMode(isPreview)` - Toggle preview mode
- `setEditorDevicePreview(device)` - Change device preview
- `toggleEditorLeftPanel()` / `toggleEditorRightPanel()` - Panel visibility

## 🚀 Usage

### Basic Implementation

```tsx
// In your editor page component
import { UnifiedEditorLayout } from '@/components/admin/unified-editor-layout'

export default function MyEditorPage() {
  return (
    <UnifiedEditorLayout editorType="page">
      <YourEditorContent />
    </UnifiedEditorLayout>
  )
}
```

### Route Detection

The system automatically detects editor routes in `app/admin/layout.tsx`:

```typescript
const isPageBuilderRoute = pathname?.match(/^\/admin\/system\/pages\/[^\/]+$/)
const isLayoutBuilderRoute = pathname?.match(/^\/admin\/system\/layouts\/editor\/[^\/]+$/)
const isUnifiedBuilderRoute = pathname?.match(/^\/admin\/system\/builders\/[^\/]+$/)
```

### Integration with Existing Builders

The unified layout integrates seamlessly with:
- `lib/page-builder/` - Page builder components
- `lib/layout-builder/` - Layout builder components  
- `lib/core/builders/` - Unified CMS builder system

## 📱 Responsive Design

### Device Preview System
- **Desktop**: Full-width editing experience
- **Tablet**: 768px constrained preview
- **Mobile**: 375px constrained preview

### Panel Management
- **Left Panel**: 15-40% width, houses blocks and AI tools
- **Right Panel**: 15-40% width, contains properties editor
- **Resizable**: Drag handles with constraints
- **Collapsible**: Toggle visibility with state persistence

## 🤖 AI Integration

### AI Assistant Features
- **Block Generation**: AI-powered block creation
- **Layout Optimization**: Intelligent layout suggestions
- **Content Enhancement**: AI content improvements
- **Responsive Adaptation**: Automatic responsive adjustments

### Integration Points
- Toolbar AI button for quick access
- Sidebar AI tabs for detailed interaction
- Context-aware AI suggestions
- Real-time AI feedback

## 🎨 Theming & Customization

### Design System
- **Shadcn UI**: Consistent component library
- **Tailwind CSS**: Utility-first styling
- **Lucide Icons**: Comprehensive icon system
- **Responsive**: Mobile-first approach

### Customization Options
- Panel widths and positions
- Toolbar button visibility
- Sidebar tab configuration
- Properties panel sections

## 🔌 Extension Points

### Adding New Editor Types

1. **Update EditorType**: Add new type to union
2. **Configure Sidebar**: Add editor-specific tabs and content
3. **Route Detection**: Add route pattern to admin layout
4. **State Management**: Extend store if needed

### Custom Sidebar Content

```tsx
// In EditorSidebar component
const renderTabContent = () => {
  if (editorType === 'your-editor') {
    switch (activeTab) {
      case 'custom':
        return <YourCustomComponent />
      // ... other cases
    }
  }
}
```

## 🧪 Testing

### Demo Route
Visit `/admin/system/builders/demo` to see the unified editor layout in action with:
- All editor interface components
- Interactive demonstrations
- Feature explanations
- Usage examples

### Development Testing
```bash
# Start development server
pnpm dev

# Visit demo page
http://localhost:3091/admin/system/builders/demo

# Test different editor routes
http://localhost:3091/admin/system/pages/new
http://localhost:3091/admin/system/layouts/editor/new
```

## 📚 Related Documentation

- [Page Builder System](../../lib/page-builder/README.md)
- [Layout Builder System](../../lib/layout-builder/README.md)
- [Unified CMS Builders](../../lib/core/builders/README.md)
- [Admin UI Store](../../stores/README.md)

## 🤝 Contributing

When adding new editor features:

1. **Follow Patterns**: Use existing component patterns
2. **State Management**: Extend AdminUI store appropriately
3. **Type Safety**: Maintain full TypeScript coverage
4. **Responsive**: Ensure mobile compatibility
5. **Accessibility**: Include proper ARIA labels and keyboard navigation

## 🔄 Migration Guide

### From Old Page Builder Layout

The old `SimplePageBuilderEditor` has been replaced with the unified system:

```tsx
// Old approach
<SimplePageBuilderEditor className="h-full" />

// New approach - handled automatically by route detection
// Content is now rendered within UnifiedEditorLayout
```

### State Migration

Editor state is now centralized in the AdminUI store:

```tsx
// Old approach - component-specific state
const [isPreviewMode, setIsPreviewMode] = useState(false)

// New approach - centralized state
const { editorIsPreviewMode, setEditorPreviewMode } = useAdminUI()
```

This unified approach provides better state management, consistency across editors, and easier maintenance.
