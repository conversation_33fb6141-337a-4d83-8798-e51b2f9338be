"use client"

import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Layout,
  Code,
  Layers,
  MousePointer,
  Smartphone,
  Monitor,
  ChevronDown,
  ChevronRight,
  CheckCircle
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { EditorType } from './unified-editor-layout'
import { usePageBuilder } from '@/lib/page-builder/context'
import { cn } from '@/lib/utils'

interface EditorPropertiesPanelProps {
  editorType: EditorType
  className?: string
}

export function EditorPropertiesPanel({
  editorType,
  className
}: EditorPropertiesPanelProps) {
  // Get page builder context if we're in page editor mode
  const pageBuilderContext = editorType === 'page' ? usePageBuilder() : null
  const selectedBlock = pageBuilderContext?.state.selectedBlockId
    ? pageBuilderContext.state.page.blocks.find(b => b.id === pageBuilderContext.state.selectedBlockId)
    : null

  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    general: true,
    styling: false,
    layout: false,
    responsive: false,
    advanced: false
  })

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Handle property updates for page builder blocks
  const handlePropertyUpdate = (property: string, value: any) => {
    if (pageBuilderContext && selectedBlock) {
      // Update block configuration or content based on property type
      if (['title', 'subtitle', 'description', 'buttonText'].includes(property)) {
        pageBuilderContext.updateBlock(selectedBlock.id, {
          content: {
            ...selectedBlock.content,
            [property]: value
          }
        })
      } else if (['padding', 'margin', 'backgroundColor', 'textColor'].includes(property)) {
        pageBuilderContext.updateBlock(selectedBlock.id, {
          styling: {
            ...selectedBlock.styling,
            [property]: value
          }
        })
      } else {
        pageBuilderContext.updateBlock(selectedBlock.id, {
          configuration: {
            ...selectedBlock.configuration,
            [property]: value
          }
        })
      }
    }
  }

  // Get element data from selected block or use mock data
  const elementData = selectedBlock ? {
    type: selectedBlock.type,
    name: selectedBlock.type.charAt(0).toUpperCase() + selectedBlock.type.slice(1).replace('-', ' '),
    id: selectedBlock.id,
    properties: {
      ...selectedBlock.configuration,
      ...selectedBlock.content,
      padding: selectedBlock.styling?.padding || { top: 20, bottom: 20, left: 20, right: 20 },
      margin: selectedBlock.styling?.margin || { top: 0, bottom: 0, left: 0, right: 0 }
    }
  } : {
    type: 'hero-section',
    name: 'Hero Section',
    id: 'hero-1',
    properties: {
      title: 'Welcome to Our Store',
      subtitle: 'Discover amazing products',
      buttonText: 'Shop Now',
      backgroundImage: '/images/hero-bg.jpg',
      textAlign: 'center',
      padding: { top: 80, bottom: 80, left: 20, right: 20 },
      margin: { top: 0, bottom: 40, left: 0, right: 0 }
    }
  }

  const renderGeneralProperties = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="element-name">Element Name</Label>
        <Input
          id="element-name"
          value={elementData.name}
          placeholder="Enter element name"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="element-id">Element ID</Label>
        <Input
          id="element-id"
          value={elementData.id}
          placeholder="Enter unique ID"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={elementData.properties.title || ''}
          placeholder="Enter title"
          onChange={(e) => handlePropertyUpdate('title', e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="subtitle">Subtitle</Label>
        <Textarea
          id="subtitle"
          value={elementData.properties.subtitle || ''}
          placeholder="Enter subtitle"
          rows={3}
          onChange={(e) => handlePropertyUpdate('subtitle', e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="button-text">Button Text</Label>
        <Input
          id="button-text"
          value={elementData.properties.buttonText || ''}
          placeholder="Enter button text"
          onChange={(e) => handlePropertyUpdate('buttonText', e.target.value)}
        />
      </div>
    </div>
  )

  const renderStylingProperties = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="text-align">Text Alignment</Label>
        <Select value={elementData.properties.textAlign}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="left">Left</SelectItem>
            <SelectItem value="center">Center</SelectItem>
            <SelectItem value="right">Right</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="background-image">Background Image</Label>
        <Input
          id="background-image"
          value={elementData.properties.backgroundImage}
          placeholder="Enter image URL"
        />
      </div>

      <div className="space-y-2">
        <Label>Background Color</Label>
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 rounded border bg-blue-500"></div>
          <Input value="#3b82f6" placeholder="#000000" />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Text Color</Label>
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 rounded border bg-white"></div>
          <Input value="#ffffff" placeholder="#000000" />
        </div>
      </div>
    </div>
  )

  const renderLayoutProperties = () => (
    <div className="space-y-4">
      <div className="space-y-3">
        <Label>Padding</Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <Label className="text-xs">Top</Label>
            <Input
              type="number"
              value={elementData.properties.padding.top}
              className="h-8"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Bottom</Label>
            <Input
              type="number"
              value={elementData.properties.padding.bottom}
              className="h-8"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Left</Label>
            <Input
              type="number"
              value={elementData.properties.padding.left}
              className="h-8"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Right</Label>
            <Input
              type="number"
              value={elementData.properties.padding.right}
              className="h-8"
            />
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <Label>Margin</Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <Label className="text-xs">Top</Label>
            <Input
              type="number"
              value={elementData.properties.margin.top}
              className="h-8"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Bottom</Label>
            <Input
              type="number"
              value={elementData.properties.margin.bottom}
              className="h-8"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Left</Label>
            <Input
              type="number"
              value={elementData.properties.margin.left}
              className="h-8"
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Right</Label>
            <Input
              type="number"
              value={elementData.properties.margin.right}
              className="h-8"
            />
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Width</Label>
        <Select defaultValue="full">
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="auto">Auto</SelectItem>
            <SelectItem value="full">Full Width</SelectItem>
            <SelectItem value="container">Container</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Height</Label>
        <Select defaultValue="auto">
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="auto">Auto</SelectItem>
            <SelectItem value="screen">Full Screen</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )

  const renderResponsiveProperties = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Monitor className="h-4 w-4" />
        <Label>Desktop Settings</Label>
        <Badge variant="outline">Active</Badge>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Visible on Desktop</Label>
          <Switch defaultChecked />
        </div>
      </div>

      <Separator />

      <div className="flex items-center space-x-2">
        <Smartphone className="h-4 w-4" />
        <Label>Mobile Settings</Label>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Visible on Mobile</Label>
          <Switch defaultChecked />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Mobile Text Size</Label>
        <Select defaultValue="normal">
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="small">Small</SelectItem>
            <SelectItem value="normal">Normal</SelectItem>
            <SelectItem value="large">Large</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )

  const renderAdvancedProperties = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="custom-css">Custom CSS</Label>
        <Textarea
          id="custom-css"
          placeholder="Enter custom CSS..."
          rows={4}
          className="font-mono text-sm"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="custom-attributes">Custom Attributes</Label>
        <Textarea
          id="custom-attributes"
          placeholder="data-attribute=value"
          rows={3}
          className="font-mono text-sm"
        />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Enable Animations</Label>
          <Switch />
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Lazy Loading</Label>
          <Switch defaultChecked />
        </div>
      </div>
    </div>
  )

  const propertySections = [
    {
      id: 'general',
      title: 'General',
      icon: Settings,
      content: renderGeneralProperties()
    },
    {
      id: 'styling',
      title: 'Styling',
      icon: Palette,
      content: renderStylingProperties()
    },
    {
      id: 'layout',
      title: 'Layout',
      icon: Layout,
      content: renderLayoutProperties()
    },
    {
      id: 'responsive',
      title: 'Responsive',
      icon: Smartphone,
      content: renderResponsiveProperties()
    },
    {
      id: 'advanced',
      title: 'Advanced',
      icon: Code,
      content: renderAdvancedProperties()
    }
  ]

  return (
    <TooltipProvider>
      <div className={cn("h-full bg-background border-l", className)}>
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span className="font-medium">Properties</span>
          </div>
          {(selectedBlock || (editorType !== 'page')) && (
            <Badge variant="secondary" className="text-xs">
              {elementData.type}
            </Badge>
          )}
        </div>

        <ScrollArea className="flex-1">
          {(selectedBlock || (editorType !== 'page')) ? (
            <div className="p-4 space-y-4">
              {/* Element Info */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Layers className="h-4 w-4" />
                    <span>{elementData.name}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>Element is valid</span>
                  </div>
                </CardContent>
              </Card>

              {/* Property Sections */}
              <div className="space-y-2">
                {propertySections.map((section) => {
                  const SectionIcon = section.icon
                  return (
                    <Collapsible
                      key={section.id}
                      open={openSections[section.id]}
                      onOpenChange={() => toggleSection(section.id)}
                    >
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          className="w-full justify-between p-3 h-auto"
                        >
                          <div className="flex items-center space-x-2">
                            <SectionIcon className="h-4 w-4" />
                            <span className="font-medium">{section.title}</span>
                          </div>
                          {openSections[section.id] ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="px-3 pb-3">
                        {section.content}
                      </CollapsibleContent>
                    </Collapsible>
                  )
                })}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full p-8">
              <div className="text-center">
                <MousePointer className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-2">No Element Selected</h3>
                <p className="text-sm text-muted-foreground">
                  Select an element to view and edit its properties
                </p>
              </div>
            </div>
          )}
        </ScrollArea>
      </div>
    </TooltipProvider>
  )
}
