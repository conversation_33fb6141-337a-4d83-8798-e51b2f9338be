"use client"

import React, { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { EditorSidebar } from './editor-sidebar'
import { EditorToolbar } from './editor-toolbar'
import { EditorPropertiesPanel } from './editor-properties-panel'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable'
import { useAdminUI } from '@/stores/use-admin-ui'
import { cn } from '@/lib/utils'

export type EditorType = 'page' | 'layout' | 'unified'

interface UnifiedEditorLayoutProps {
  children: React.ReactNode
  editorType: EditorType
  className?: string
}

export function UnifiedEditorLayout({
  children,
  editorType,
  className
}: UnifiedEditorLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const {
    editorLeftPanelO<PERSON>,
    editorRightPanelO<PERSON>,
    editorLeft<PERSON>anelWidth,
    editorRightPanelWidth,
    setEditorMode,
    setEditorLeftPanelWidth,
    setEditorRightPanelWidth
  } = useAdminUI()

  const [leftSize, setLeftSize] = useState(editorLeftPanelWidth)
  const [rightSize, setRightSize] = useState(editorRightPanelWidth)

  // Set editor mode when component mounts
  useEffect(() => {
    setEditorMode(true, editorType)

    // Cleanup when component unmounts
    return () => {
      setEditorMode(false)
    }
  }, [setEditorMode, editorType])

  // Update panel sizes when they change
  useEffect(() => {
    setEditorLeftPanelWidth(leftSize)
  }, [leftSize, setEditorLeftPanelWidth])

  useEffect(() => {
    setEditorRightPanelWidth(rightSize)
  }, [rightSize, setEditorRightPanelWidth])

  // Handle back navigation based on editor type
  const handleBackToAdmin = () => {
    switch (editorType) {
      case 'page':
        router.push('/admin/page-builder')
        break
      case 'layout':
        router.push('/admin/layout-builder')
        break
      case 'unified':
        router.push('/admin/system/builders')
        break
      default:
        router.push('/admin')
    }
  }

  return (
    <div className={cn("h-screen flex flex-col overflow-hidden", className)}>
      <SidebarProvider>
        {/* Editor Sidebar */}
        <EditorSidebar 
          editorType={editorType}
          onBackToAdmin={handleBackToAdmin}
        />
        
        <SidebarInset className="flex-1 overflow-hidden">
          {/* Editor Toolbar */}
          <EditorToolbar editorType={editorType} />
          
          {/* Main Editor Content with Resizable Panels */}
          <div className="flex-1 overflow-hidden">
            <ResizablePanelGroup
              direction="horizontal"
              className="h-full"
              onLayout={(sizes) => {
                const [left, , right] = sizes
                if (left && editorLeftPanelOpen) setLeftSize(left)
                if (right && editorRightPanelOpen) setRightSize(right)
              }}
            >
              {/* Left Panel - Block Library / AI Tools */}
              {editorLeftPanelOpen && (
                <>
                  <ResizablePanel
                    defaultSize={leftSize}
                    minSize={15}
                    maxSize={40}
                    className="overflow-hidden"
                  >
                    <div className="h-full bg-background border-r">
                      {/* Left panel content will be rendered by the editor sidebar */}
                    </div>
                  </ResizablePanel>
                  <ResizableHandle withHandle />
                </>
              )}

              {/* Main Canvas Area */}
              <ResizablePanel className="overflow-hidden">
                <div className="h-full bg-muted/30">
                  {children}
                </div>
              </ResizablePanel>

              {/* Right Panel - Properties */}
              {editorRightPanelOpen && (
                <>
                  <ResizableHandle withHandle />
                  <ResizablePanel
                    defaultSize={rightSize}
                    minSize={15}
                    maxSize={40}
                    className="overflow-hidden"
                  >
                    <EditorPropertiesPanel editorType={editorType} />
                  </ResizablePanel>
                </>
              )}
            </ResizablePanelGroup>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}
