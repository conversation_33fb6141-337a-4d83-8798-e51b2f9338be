"use client"

import React, { Suspense } from 'react'
import { Layers, Package } from 'lucide-react'

// Safe wrapper for BlockLibrary that handles context availability
export function SafeBlockLibrary() {
  return (
    <Suspense fallback={<BlockLibraryFallback />}>
      <BlockLibraryWrapper />
    </Suspense>
  )
}

// Safe wrapper for ComponentTree that handles context availability
export function SafeComponentTree() {
  return (
    <Suspense fallback={<ComponentTreeFallback />}>
      <ComponentTreeWrapper />
    </Suspense>
  )
}

// Wrapper component that only renders when context is available
function BlockLibraryWrapper() {
  try {
    // Check if we can access the context
    const { usePageBuilder } = require('@/lib/page-builder/context')
    
    // Create a component that uses the hook safely
    const BlockLibraryWithContext = () => {
      try {
        usePageBuilder() // This will throw if context is not available
        
        // Only import and render the actual component if context is available
        const { BlockLibrary } = require('@/lib/page-builder/components/block-library')
        return <BlockLibrary />
      } catch (error) {
        return <BlockLibraryFallback />
      }
    }
    
    return <BlockLibraryWithContext />
  } catch (error) {
    return <BlockLibraryFallback />
  }
}

// Wrapper component for ComponentTree
function ComponentTreeWrapper() {
  try {
    // Check if we can access the context
    const { usePageBuilder } = require('@/lib/page-builder/context')
    
    // Create a component that uses the hook safely
    const ComponentTreeWithContext = () => {
      try {
        usePageBuilder() // This will throw if context is not available
        
        // Only import and render the actual component if context is available
        const { ComponentTree } = require('@/lib/page-builder/components/component-tree')
        return <ComponentTree />
      } catch (error) {
        return <ComponentTreeFallback />
      }
    }
    
    return <ComponentTreeWithContext />
  } catch (error) {
    return <ComponentTreeFallback />
  }
}

// Fallback component for BlockLibrary
function BlockLibraryFallback() {
  return (
    <div className="p-4">
      <div className="text-center py-8 text-gray-400">
        <Package className="h-8 w-8 mx-auto mb-2" />
        <p className="text-sm">Block Library</p>
        <p className="text-xs text-muted-foreground mt-1">
          Available when editing pages
        </p>
      </div>
    </div>
  )
}

// Fallback component for ComponentTree
function ComponentTreeFallback() {
  return (
    <div className="p-4">
      <div className="text-center py-8 text-gray-400">
        <Layers className="h-8 w-8 mx-auto mb-2" />
        <p className="text-sm">Component Tree</p>
        <p className="text-xs text-muted-foreground mt-1">
          Available when editing pages
        </p>
      </div>
    </div>
  )
}
