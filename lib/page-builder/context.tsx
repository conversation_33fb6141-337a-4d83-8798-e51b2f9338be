'use client'

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import {
  PageBuilderState,
  PageBuilderAction,
  PageData,
  PageBlock,
  BlockConfiguration
} from './types'
import { generateId } from './utils'
import { blockRegistry } from './blocks/registry'
import { useAdminUI } from '@/stores/use-admin-ui'

// Initial state
const initialState: PageBuilderState = {
  page: {
    title: 'New Page',
    slug: 'new-page',
    status: 'draft',
    type: 'custom',
    blocks: [],
    settings: {
      title: 'New Page',
    }
  },
  selectedBlockId: null,
  isDragging: false,
  isPreviewMode: false,
  devicePreview: 'desktop',
  history: [],
  historyIndex: -1,
  isSaving: false,
  hasUnsavedChanges: false,
}

// Reducer
function pageBuilderReducer(state: PageBuilderState, action: PageBuilderAction): PageBuilderState {
  switch (action.type) {
    case 'SET_PAGE':
      return {
        ...state,
        page: action.payload,
        hasUnsavedChanges: false,
        history: [{ blocks: action.payload.blocks, settings: action.payload.settings }],
        historyIndex: 0,
      }

    case 'ADD_BLOCK': {
      const { blockType, position } = action.payload
      const blockDefinition = blockRegistry.getBlockType(blockType)
      
      if (!blockDefinition) {
        console.error(`Block type "${blockType}" not found in registry`)
        return state
      }

      const newBlock: PageBlock = {
        id: generateId(),
        type: blockType,
        position: position ?? state.page.blocks.length,
        isVisible: true,
        configuration: blockDefinition.defaultConfig,
        content: {},
        styling: {},
        responsive: {},
        animation: {},
        conditions: {},
      }

      const updatedBlocks = [...state.page.blocks]
      if (position !== undefined) {
        // Insert at specific position
        updatedBlocks.splice(position, 0, newBlock)
        // Update positions of subsequent blocks
        for (let i = position + 1; i < updatedBlocks.length; i++) {
          updatedBlocks[i].position = i
        }
      } else {
        // Add to end
        updatedBlocks.push(newBlock)
      }

      const newState = {
        ...state,
        page: {
          ...state.page,
          blocks: updatedBlocks,
        },
        selectedBlockId: newBlock.id,
        hasUnsavedChanges: true,
      }

      return addToHistory(newState)
    }

    case 'UPDATE_BLOCK': {
      const { id, updates } = action.payload
      const updatedBlocks = state.page.blocks.map(block =>
        block.id === id ? { ...block, ...updates } : block
      )

      const newState = {
        ...state,
        page: {
          ...state.page,
          blocks: updatedBlocks,
        },
        hasUnsavedChanges: true,
      }

      return addToHistory(newState)
    }

    case 'DELETE_BLOCK': {
      const { id } = action.payload
      const updatedBlocks = state.page.blocks
        .filter(block => block.id !== id)
        .map((block, index) => ({ ...block, position: index }))

      const newState = {
        ...state,
        page: {
          ...state.page,
          blocks: updatedBlocks,
        },
        selectedBlockId: state.selectedBlockId === id ? null : state.selectedBlockId,
        hasUnsavedChanges: true,
      }

      return addToHistory(newState)
    }

    case 'MOVE_BLOCK': {
      const { id, newPosition } = action.payload
      const blockIndex = state.page.blocks.findIndex(block => block.id === id)
      
      if (blockIndex === -1) return state

      const updatedBlocks = [...state.page.blocks]
      const [movedBlock] = updatedBlocks.splice(blockIndex, 1)
      updatedBlocks.splice(newPosition, 0, movedBlock)

      // Update positions
      updatedBlocks.forEach((block, index) => {
        block.position = index
      })

      const newState = {
        ...state,
        page: {
          ...state.page,
          blocks: updatedBlocks,
        },
        hasUnsavedChanges: true,
      }

      return addToHistory(newState)
    }

    case 'SELECT_BLOCK':
      return {
        ...state,
        selectedBlockId: action.payload.id,
      }

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        isPreviewMode: action.payload.enabled,
        selectedBlockId: action.payload.enabled ? null : state.selectedBlockId,
      }

    case 'SET_DEVICE_PREVIEW':
      return {
        ...state,
        devicePreview: action.payload.device,
      }

    case 'SAVE_PAGE':
      return {
        ...state,
        isSaving: action.payload.saving,
        hasUnsavedChanges: action.payload.saving ? state.hasUnsavedChanges : false,
      }

    case 'UNDO': {
      if (state.historyIndex <= 0) return state

      const newIndex = state.historyIndex - 1
      const historyItem = state.history[newIndex]

      return {
        ...state,
        page: {
          ...state.page,
          blocks: historyItem.blocks,
          settings: historyItem.settings,
        },
        historyIndex: newIndex,
        hasUnsavedChanges: true,
      }
    }

    case 'REDO': {
      if (state.historyIndex >= state.history.length - 1) return state

      const newIndex = state.historyIndex + 1
      const historyItem = state.history[newIndex]

      return {
        ...state,
        page: {
          ...state.page,
          blocks: historyItem.blocks,
          settings: historyItem.settings,
        },
        historyIndex: newIndex,
        hasUnsavedChanges: true,
      }
    }

    default:
      return state
  }
}

// Helper function to add state to history
function addToHistory(state: PageBuilderState): PageBuilderState {
  const maxHistorySize = 50
  const newHistoryItem = {
    blocks: state.page.blocks,
    settings: state.page.settings,
  }

  let newHistory = [...state.history.slice(0, state.historyIndex + 1), newHistoryItem]
  
  // Limit history size
  if (newHistory.length > maxHistorySize) {
    newHistory = newHistory.slice(-maxHistorySize)
  }

  return {
    ...state,
    history: newHistory,
    historyIndex: newHistory.length - 1,
  }
}

// Context
interface PageBuilderContextType {
  state: PageBuilderState
  dispatch: React.Dispatch<PageBuilderAction>
  
  // Helper functions
  addBlock: (blockType: string, position?: number) => void
  updateBlock: (id: string, updates: Partial<PageBlock>) => void
  deleteBlock: (id: string) => void
  moveBlock: (id: string, newPosition: number) => void
  selectBlock: (id: string | null) => void
  setPreviewMode: (enabled: boolean) => void
  setDevicePreview: (device: 'desktop' | 'tablet' | 'mobile') => void
  updateBlockConfiguration: (id: string, configuration: BlockConfiguration) => void
  duplicateBlock: (id: string) => void
  undo: () => void
  redo: () => void
  canUndo: boolean
  canRedo: boolean
}

const PageBuilderContext = createContext<PageBuilderContextType | undefined>(undefined)

// Provider
interface PageBuilderProviderProps {
  children: React.ReactNode
  initialPage?: PageData
}

export function PageBuilderProvider({ children, initialPage }: PageBuilderProviderProps) {
  const [state, dispatch] = useReducer(pageBuilderReducer, {
    ...initialState,
    page: initialPage || initialState.page,
  })

  // Get admin UI state for integration
  const {
    editorIsPreviewMode,
    editorDevicePreview,
    editorHasUnsavedChanges,
    editorCanUndo,
    editorCanRedo,
    setEditorHasUnsavedChanges,
    setEditorCanUndo,
    setEditorCanRedo,
    setEditorPreviewMode,
    setEditorDevicePreview
  } = useAdminUI()

  // Initialize with initial page if provided
  useEffect(() => {
    if (initialPage) {
      dispatch({ type: 'SET_PAGE', payload: initialPage })
    }
  }, [initialPage])

  // Calculate undo/redo state
  const canUndo = state.historyIndex > 0
  const canRedo = state.historyIndex < state.history.length - 1

  // Sync page builder state with admin UI state
  useEffect(() => {
    setEditorHasUnsavedChanges(state.hasUnsavedChanges)
    setEditorCanUndo(canUndo)
    setEditorCanRedo(canRedo)
  }, [state.hasUnsavedChanges, canUndo, canRedo, setEditorHasUnsavedChanges, setEditorCanUndo, setEditorCanRedo])

  // Sync admin UI state with page builder
  useEffect(() => {
    if (state.isPreviewMode !== editorIsPreviewMode) {
      dispatch({ type: 'SET_PREVIEW_MODE', payload: { enabled: editorIsPreviewMode } })
    }
  }, [editorIsPreviewMode, state.isPreviewMode])

  useEffect(() => {
    if (state.devicePreview !== editorDevicePreview) {
      dispatch({ type: 'SET_DEVICE_PREVIEW', payload: { device: editorDevicePreview } })
    }
  }, [editorDevicePreview, state.devicePreview])

  // Helper functions
  const addBlock = useCallback((blockType: string, position?: number) => {
    dispatch({ type: 'ADD_BLOCK', payload: { blockType, position } })
  }, [])

  const updateBlock = useCallback((id: string, updates: Partial<PageBlock>) => {
    dispatch({ type: 'UPDATE_BLOCK', payload: { id, updates } })
  }, [])

  const deleteBlock = useCallback((id: string) => {
    dispatch({ type: 'DELETE_BLOCK', payload: { id } })
  }, [])

  const moveBlock = useCallback((id: string, newPosition: number) => {
    dispatch({ type: 'MOVE_BLOCK', payload: { id, newPosition } })
  }, [])

  const selectBlock = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_BLOCK', payload: { id } })
  }, [])

  const setPreviewMode = useCallback((enabled: boolean) => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: { enabled } })
  }, [])

  const setDevicePreview = useCallback((device: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_DEVICE_PREVIEW', payload: { device } })
  }, [])

  const updateBlockConfiguration = useCallback((id: string, configuration: BlockConfiguration) => {
    dispatch({ type: 'UPDATE_BLOCK', payload: { id, updates: { configuration } } })
  }, [])

  const duplicateBlock = useCallback((id: string) => {
    const block = state.page.blocks.find(b => b.id === id)
    if (block) {
      const newBlock: PageBlock = {
        ...block,
        id: generateId(),
        position: block.position + 1,
      }
      
      const updatedBlocks = [...state.page.blocks]
      updatedBlocks.splice(block.position + 1, 0, newBlock)
      
      // Update positions
      for (let i = block.position + 2; i < updatedBlocks.length; i++) {
        updatedBlocks[i].position = i
      }

      dispatch({ 
        type: 'SET_PAGE', 
        payload: { 
          ...state.page, 
          blocks: updatedBlocks 
        } 
      })
    }
  }, [state.page.blocks])

  const undo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const redo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const value: PageBuilderContextType = {
    state,
    dispatch,
    addBlock,
    updateBlock,
    deleteBlock,
    moveBlock,
    selectBlock,
    setPreviewMode,
    setDevicePreview,
    updateBlockConfiguration,
    duplicateBlock,
    undo,
    redo,
    canUndo,
    canRedo,
  }

  return (
    <PageBuilderContext.Provider value={value}>
      {children}
    </PageBuilderContext.Provider>
  )
}

// Hook
export function usePageBuilder() {
  const context = useContext(PageBuilderContext)
  if (context === undefined) {
    throw new Error('usePageBuilder must be used within a PageBuilderProvider')
  }
  return context
}
