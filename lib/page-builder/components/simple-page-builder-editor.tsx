'use client'

import React, { useState } from 'react'
import { usePageBuilder } from '../context'
import { PageBuilderCanvas } from './page-builder-canvas'
import { FloatingAIChat } from './floating-ai-chat'
import { Button } from '@/components/ui/button'
import { EditorLayout } from '@/components/admin/editor-layout'
import { PageBuilderSidebar } from "@/components/admin/page-builder-sidebar"
import { PageBuilderPropertiesSidebar } from "@/components/admin/page-builder-properties-sidebar"

import {
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  Sparkles
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SimplePageBuilderEditorProps {
  className?: string
}

export function SimplePageBuilderEditor({
  className
}: SimplePageBuilderEditorProps) {
  const {
    state,
    setPreviewMode,
    setDevicePreview,
    undo,
    redo
  } = usePageBuilder()

  const { isPreviewMode, devicePreview, hasUnsavedChanges, canUndo, canRedo } = state
  const [isAIChatOpen, setIsAIChatOpen] = useState(false)

  // Device preview options
  const deviceOptions = [
    { value: 'desktop' as const, icon: Monitor, label: 'Desktop' },
    { value: 'tablet' as const, icon: Tablet, label: 'Tablet' },
    { value: 'mobile' as const, icon: Smartphone, label: 'Mobile' },
  ]

  const leftPanelContent = (
    <div className="flex h-full flex-col">
      <PageBuilderSidebar />
    </div>
  )

  const rightPanelContent = (
    <div className="flex h-full flex-col">
      <PageBuilderPropertiesSidebar />
    </div>
  )

  return (
    <EditorLayout
      className={className}
      leftPanel={leftPanelContent}
      rightPanel={rightPanelContent}
    >
      <div className="flex-1 overflow-hidden">
        <PageBuilderCanvas
          devicePreview={devicePreview}
          isPreviewMode={isPreviewMode}
        />
      </div>

      {/* Floating AI Chat */}
      <FloatingAIChat
        isOpen={isAIChatOpen}
        onClose={() => setIsAIChatOpen(false)}
      />
      
      {/* AI Designer Toggle - Moved to bottom right */}
      <Button
        variant={isAIChatOpen ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setIsAIChatOpen(!isAIChatOpen)}
        className={cn(
          'fixed bottom-4 right-4',
          isAIChatOpen && 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white'
        )}
        title="AI Page Designer"
      >
        <Sparkles className="h-4 w-4 mr-2" />
        AI Designer
        {isAIChatOpen && (
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
        )}
      </Button>
    </EditorLayout>
  )
}
