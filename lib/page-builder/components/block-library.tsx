'use client'

import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react'
import { usePageBuilder } from '../context'
import { blockRegistry } from '../blocks/registry'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import {
  Search,
  Package,
  Image,
  Type,
  ShoppingCart,
  Layout,
  Share2,
  Settings,
  Zap,
  Plus,
  Filter,
  Grid3X3,
  List,
  Star,
  Clock,
  TrendingUp,
  <PERSON>rkles,
  ChevronDown,
  X,
  Heart,
  MoreHorizontal,
  Copy,
  Eye,
  Info
} from 'lucide-react'
import { cn } from '@/lib/utils'

type ViewMode = 'grid' | 'list'
type SortOption = 'name' | 'category' | 'recent' | 'popular'

export function BlockLibrary() {
  const { addBlock } = usePageBuilder()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortOption>('category')
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const [recentBlocks, setRecentBlocks] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set())
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Get all block types
  const allBlocks = blockRegistry.getAllBlockTypes()
  const categories = ['all', 'favorites', 'recent', ...blockRegistry.getCategories()]

  // Enhanced category configuration with modern icons and gradients
  const categoryConfig: Record<string, { 
    name: string; 
    icon: any; 
    gradient: string; 
    description: string;
    count?: number;
  }> = {
    all: { 
      name: 'All Blocks', 
      icon: Package, 
      gradient: 'from-slate-500 to-slate-600', 
      description: 'Browse all available blocks',
      count: allBlocks.length
    },
    favorites: { 
      name: 'Favorites', 
      icon: Heart, 
      gradient: 'from-rose-500 to-pink-600', 
      description: 'Your favorite blocks',
      count: favorites.size
    },
    recent: { 
      name: 'Recent', 
      icon: Clock, 
      gradient: 'from-violet-500 to-purple-600', 
      description: 'Recently used blocks',
      count: recentBlocks.length
    },
    content: { 
      name: 'Content', 
      icon: Type, 
      gradient: 'from-blue-500 to-cyan-600', 
      description: 'Text and content blocks' 
    },
    layout: { 
      name: 'Layout', 
      icon: Layout, 
      gradient: 'from-emerald-500 to-teal-600', 
      description: 'Structure and layout blocks' 
    },
    media: { 
      name: 'Media', 
      icon: Image, 
      gradient: 'from-purple-500 to-indigo-600', 
      description: 'Images and media blocks' 
    },
    ecommerce: { 
      name: 'E-commerce', 
      icon: ShoppingCart, 
      gradient: 'from-orange-500 to-red-600', 
      description: 'Shopping and product blocks' 
    },
    marketing: { 
      name: 'Marketing', 
      icon: Zap, 
      gradient: 'from-yellow-500 to-orange-600', 
      description: 'Promotional and marketing blocks' 
    },
    social: { 
      name: 'Social', 
      icon: Share2, 
      gradient: 'from-indigo-500 to-blue-600', 
      description: 'Social media blocks' 
    },
    utility: { 
      name: 'Utility', 
      icon: Settings, 
      gradient: 'from-gray-500 to-slate-600', 
      description: 'Utility and functional blocks' 
    }
  }

  // Get all unique tags
  const allTags = useMemo(() => {
    const tags = new Set<string>()
    allBlocks.forEach(block => {
      block.tags?.forEach(tag => tags.add(tag))
    })
    return Array.from(tags).sort()
  }, [allBlocks])

  // Enhanced filtering and sorting logic
  const filteredAndSortedBlocks = useMemo(() => {
    let filtered = allBlocks.filter(block => {
      // Search filter
      const matchesSearch = searchQuery === '' ||
        block.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      // Category filter
      let matchesCategory = true
      if (selectedCategory === 'favorites') {
        matchesCategory = favorites.has(block.id)
      } else if (selectedCategory === 'recent') {
        matchesCategory = recentBlocks.includes(block.id)
      } else if (selectedCategory !== 'all') {
        matchesCategory = block.category === selectedCategory
      }

      // Tags filter
      const matchesTags = selectedTags.size === 0 || 
        block.tags?.some(tag => selectedTags.has(tag))

      return matchesSearch && matchesCategory && matchesTags
    })

    // Sort blocks
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.displayName.localeCompare(b.displayName)
        case 'category':
          return a.category.localeCompare(b.category) || a.displayName.localeCompare(b.displayName)
        case 'recent':
          const aIndex = recentBlocks.indexOf(a.id)
          const bIndex = recentBlocks.indexOf(b.id)
          if (aIndex === -1 && bIndex === -1) return a.displayName.localeCompare(b.displayName)
          if (aIndex === -1) return 1
          if (bIndex === -1) return -1
          return aIndex - bIndex
        case 'popular':
          // For now, sort by favorites then name
          const aFav = favorites.has(a.id)
          const bFav = favorites.has(b.id)
          if (aFav && !bFav) return -1
          if (!aFav && bFav) return 1
          return a.displayName.localeCompare(b.displayName)
        default:
          return 0
      }
    })

    return filtered
  }, [allBlocks, searchQuery, selectedCategory, selectedTags, sortBy, favorites, recentBlocks])

  // Handle block drag start
  const handleDragStart = useCallback((e: React.DragEvent, blockType: string) => {
    e.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'new-block',
      blockType
    }))
    e.dataTransfer.effectAllowed = 'copy'
    
    // Add visual feedback
    e.currentTarget.classList.add('opacity-50')
  }, [])

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    e.currentTarget.classList.remove('opacity-50')
  }, [])

  // Handle block click (add to canvas)
  const handleBlockClick = useCallback((blockType: string, blockId: string) => {
    addBlock(blockType)
    
    // Add to recent blocks
    setRecentBlocks(prev => {
      const filtered = prev.filter(id => id !== blockId)
      return [blockId, ...filtered].slice(0, 10) // Keep last 10
    })
  }, [addBlock])

  // Toggle favorite
  const toggleFavorite = useCallback((blockId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setFavorites(prev => {
      const newFavorites = new Set(prev)
      if (newFavorites.has(blockId)) {
        newFavorites.delete(blockId)
      } else {
        newFavorites.add(blockId)
      }
      return newFavorites
    })
  }, [])

  // Toggle tag filter
  const toggleTag = useCallback((tag: string) => {
    setSelectedTags(prev => {
      const newTags = new Set(prev)
      if (newTags.has(tag)) {
        newTags.delete(tag)
      } else {
        newTags.add(tag)
      }
      return newTags
    })
  }, [])

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchQuery('')
    setSelectedTags(new Set())
    setSelectedCategory('all')
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        searchInputRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  return (
    <TooltipProvider>
      <div className="h-full flex flex-col bg-gradient-to-br from-slate-50 to-white border-r border-slate-200">
        {/* Modern Header */}
        <div className="px-4 py-4 border-b border-slate-200 bg-white/80 backdrop-blur-sm">
          {/* Title */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Package className="h-4 w-4 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-slate-900">Block Library</h2>
                <p className="text-xs text-slate-500">{filteredAndSortedBlocks.length} blocks available</p>
              </div>
            </div>
            
            {/* View Controls */}
            <div className="flex items-center gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="h-8 w-8 p-0"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Grid View</TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="h-8 w-8 p-0"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>List View</TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Enhanced Search Bar */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search blocks... (⌘K)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-10 py-2.5 text-sm border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/50 backdrop-blur-sm transition-all"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Filters and Sort */}
          <div className="flex items-center gap-2">
            <Popover open={showFilters} onOpenChange={setShowFilters}>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-8">
                  <Filter className="h-3 w-3 mr-1" />
                  Filters
                  {selectedTags.size > 0 && (
                    <Badge variant="secondary" className="ml-1 h-4 px-1 text-xs">
                      {selectedTags.size}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="start">
                <Command>
                  <CommandInput placeholder="Search tags..." />
                  <CommandList>
                    <CommandEmpty>No tags found.</CommandEmpty>
                    <CommandGroup heading="Tags">
                      {allTags.map((tag) => (
                        <CommandItem
                          key={tag}
                          onSelect={() => toggleTag(tag)}
                          className="flex items-center gap-2"
                        >
                          <div className={cn(
                            "w-4 h-4 border rounded flex items-center justify-center",
                            selectedTags.has(tag) && "bg-blue-500 border-blue-500"
                          )}>
                            {selectedTags.has(tag) && <div className="w-2 h-2 bg-white rounded-full" />}
                          </div>
                          <span className="capitalize">{tag}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-8">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Sort
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48 p-1" align="start">
                {[
                  { value: 'category', label: 'By Category', icon: Layout },
                  { value: 'name', label: 'By Name', icon: Type },
                  { value: 'recent', label: 'Recently Used', icon: Clock },
                  { value: 'popular', label: 'Most Popular', icon: Star },
                ].map((option) => (
                  <Button
                    key={option.value}
                    variant={sortBy === option.value ? 'secondary' : 'ghost'}
                    size="sm"
                    onClick={() => setSortBy(option.value as SortOption)}
                    className="w-full justify-start h-8"
                  >
                    <option.icon className="h-3 w-3 mr-2" />
                    {option.label}
                  </Button>
                ))}
              </PopoverContent>
            </Popover>

            {(searchQuery || selectedTags.size > 0 || selectedCategory !== 'all') && (
              <Button variant="ghost" size="sm" onClick={clearFilters} className="h-8 text-slate-500">
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
          </div>

          {/* Active Filters */}
          {selectedTags.size > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {Array.from(selectedTags).map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs cursor-pointer hover:bg-slate-200"
                  onClick={() => toggleTag(tag)}
                >
                  {tag}
                  <X className="h-3 w-3 ml-1" />
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Modern Categories */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="h-full flex flex-col">
            {/* Category Tabs */}
            <div className="border-b border-slate-200 bg-white/50 backdrop-blur-sm">
              <ScrollArea className="w-full">
                <TabsList className="inline-flex h-auto p-1 bg-transparent gap-1">
                  {categories.map((category) => {
                    const config = categoryConfig[category]
                    const Icon = config?.icon || Package
                    const count = config?.count || allBlocks.filter(b => b.category === category).length

                    return (
                      <TabsTrigger
                        key={category}
                        value={category}
                        className={cn(
                          'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg border-0',
                          'data-[state=active]:bg-white data-[state=active]:shadow-sm',
                          'hover:bg-white/50 transition-all duration-200',
                          'data-[state=active]:text-slate-900 text-slate-600'
                        )}
                      >
                        <div className={cn(
                          'w-5 h-5 rounded-md flex items-center justify-center bg-gradient-to-br',
                          config?.gradient || 'from-gray-400 to-gray-500'
                        )}>
                          <Icon className="h-3 w-3 text-white" />
                        </div>
                        <span>{config?.name || category}</span>
                        {count > 0 && (
                          <Badge variant="secondary" className="h-4 px-1.5 text-xs">
                            {count}
                          </Badge>
                        )}
                      </TabsTrigger>
                    )
                  })}
                </TabsList>
              </ScrollArea>
            </div>

            {/* Block Content */}
            <div className="flex-1 overflow-hidden">
              <TabsContent value={selectedCategory} className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col">
                <ScrollArea className="flex-1 p-4">
                  {filteredAndSortedBlocks.length === 0 ? (
                    <div className="text-center py-16">
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
                        <Package className="h-8 w-8 text-slate-400" />
                      </div>
                      <h3 className="text-lg font-medium text-slate-900 mb-2">No blocks found</h3>
                      <p className="text-sm text-slate-500 mb-4">
                        {searchQuery ? 'Try adjusting your search or filters' : 'No blocks in this category'}
                      </p>
                      {(searchQuery || selectedTags.size > 0) && (
                        <Button variant="outline" size="sm" onClick={clearFilters}>
                          Clear filters
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className={cn(
                      viewMode === 'grid' 
                        ? 'grid grid-cols-2 gap-3' 
                        : 'space-y-2'
                    )}>
                      {filteredAndSortedBlocks.map((block) => (
                        <ModernBlockItem
                          key={block.id}
                          block={block}
                          viewMode={viewMode}
                          isFavorite={favorites.has(block.id)}
                          isRecent={recentBlocks.includes(block.id)}
                          onDragStart={(e: React.DragEvent) => handleDragStart(e, block.name)}
                          onDragEnd={handleDragEnd}
                          onClick={() => handleBlockClick(block.name, block.id)}
                          onToggleFavorite={(e) => toggleFavorite(block.id, e)}
                        />
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </TooltipProvider>
  )
}

// Modern block item component
interface ModernBlockItemProps {
  block: any
  viewMode: ViewMode
  isFavorite: boolean
  isRecent: boolean
  onDragStart: (e: React.DragEvent) => void
  onDragEnd: (e: React.DragEvent) => void
  onClick: () => void
  onToggleFavorite: (e: React.MouseEvent) => void
}

function ModernBlockItem({ 
  block, 
  viewMode, 
  isFavorite, 
  isRecent, 
  onDragStart, 
  onDragEnd, 
  onClick, 
  onToggleFavorite 
}: ModernBlockItemProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [showActions, setShowActions] = useState(false)

  const categoryConfig = {
    content: { gradient: 'from-blue-500 to-cyan-600' },
    layout: { gradient: 'from-emerald-500 to-teal-600' },
    media: { gradient: 'from-purple-500 to-indigo-600' },
    ecommerce: { gradient: 'from-orange-500 to-red-600' },
    marketing: { gradient: 'from-yellow-500 to-orange-600' },
    social: { gradient: 'from-indigo-500 to-blue-600' },
    utility: { gradient: 'from-gray-500 to-slate-600' },
  }

  const categoryGradient = categoryConfig[block.category as keyof typeof categoryConfig]?.gradient || 'from-gray-400 to-gray-500'

  if (viewMode === 'list') {
    return (
      <div
        className={cn(
          'group relative bg-white/80 backdrop-blur-sm border border-slate-200 rounded-xl p-4 cursor-pointer transition-all duration-300',
          'hover:border-blue-300 hover:shadow-lg hover:shadow-blue-100/50 hover:bg-white',
          'active:scale-[0.98] active:shadow-sm',
          isHovered && 'border-blue-300 shadow-lg shadow-blue-100/50 bg-white'
        )}
        draggable
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-center gap-4">
          {/* Block Icon */}
          <div className={cn(
            'w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br shadow-sm',
            categoryGradient
          )}>
            <span className="text-xl filter drop-shadow-sm">{block.icon || '📦'}</span>
          </div>

          {/* Block Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-semibold text-slate-900 truncate">
                {block.displayName}
              </h3>
              {isRecent && (
                <Badge variant="secondary" className="h-4 px-1.5 text-xs bg-violet-100 text-violet-700">
                  Recent
                </Badge>
              )}
            </div>
            <p className="text-xs text-slate-500 line-clamp-1">
              {block.description || 'No description available'}
            </p>
            {block.tags && block.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {block.tags.slice(0, 3).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="h-4 px-1.5 text-xs">
                    {tag}
                  </Badge>
                ))}
                {block.tags.length > 3 && (
                  <Badge variant="outline" className="h-4 px-1.5 text-xs">
                    +{block.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleFavorite}
                  className={cn(
                    "h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",
                    isFavorite && "opacity-100 text-rose-500 hover:text-rose-600"
                  )}
                >
                  <Heart className={cn("h-4 w-4", isFavorite && "fill-current")} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isFavorite ? 'Remove from favorites' : 'Add to favorites'}
              </TooltipContent>
            </Tooltip>

            <Popover open={showActions} onOpenChange={setShowActions}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48 p-1" align="end">
                <Button variant="ghost" size="sm" className="w-full justify-start h-8">
                  <Eye className="h-3 w-3 mr-2" />
                  Preview
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start h-8">
                  <Copy className="h-3 w-3 mr-2" />
                  Duplicate
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start h-8">
                  <Info className="h-3 w-3 mr-2" />
                  Details
                </Button>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Hover overlay */}
        <div className={cn(
          'absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-xl opacity-0 transition-opacity pointer-events-none',
          isHovered && 'opacity-100'
        )} />
      </div>
    )
  }

  // Grid view
  return (
    <div
      className={cn(
        'group relative bg-white/80 backdrop-blur-sm border border-slate-200 rounded-xl p-4 cursor-pointer transition-all duration-300',
        'hover:border-blue-300 hover:shadow-lg hover:shadow-blue-100/50 hover:bg-white hover:-translate-y-1',
        'active:scale-[0.95] active:shadow-sm active:translate-y-0',
        isHovered && 'border-blue-300 shadow-lg shadow-blue-100/50 bg-white -translate-y-1'
      )}
      draggable
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Favorite indicator */}
      {isFavorite && (
        <div className="absolute top-2 right-2 z-10">
          <div className="w-5 h-5 rounded-full bg-rose-500 flex items-center justify-center">
            <Heart className="h-3 w-3 text-white fill-current" />
          </div>
        </div>
      )}

      {/* Recent indicator */}
      {isRecent && !isFavorite && (
        <div className="absolute top-2 right-2 z-10">
          <div className="w-5 h-5 rounded-full bg-violet-500 flex items-center justify-center">
            <Clock className="h-3 w-3 text-white" />
          </div>
        </div>
      )}

      {/* Block content */}
      <div className="flex flex-col items-center text-center space-y-3">
        {/* Block Icon with gradient background */}
        <div className={cn(
          'w-14 h-14 rounded-2xl flex items-center justify-center bg-gradient-to-br shadow-lg transition-transform duration-300',
          categoryGradient,
          'group-hover:scale-110 group-hover:shadow-xl'
        )}>
          <span className="text-2xl filter drop-shadow-sm">{block.icon || '📦'}</span>
        </div>

        {/* Block Name */}
        <div className="w-full">
          <h3 className="text-sm font-semibold text-slate-900 truncate mb-1">
            {block.displayName}
          </h3>
          <p className="text-xs text-slate-500 line-clamp-2 leading-relaxed">
            {block.description || 'No description available'}
          </p>
        </div>

        {/* Tags */}
        {block.tags && block.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 justify-center">
            {block.tags.slice(0, 2).map((tag: string) => (
              <Badge key={tag} variant="outline" className="h-4 px-1.5 text-xs">
                {tag}
              </Badge>
            ))}
            {block.tags.length > 2 && (
              <Badge variant="outline" className="h-4 px-1.5 text-xs">
                +{block.tags.length - 2}
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Action buttons - shown on hover */}
      <div className={cn(
        'absolute bottom-2 left-2 right-2 flex items-center justify-between opacity-0 transition-opacity duration-200',
        'group-hover:opacity-100'
      )}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleFavorite}
              className={cn(
                "h-7 w-7 p-0 bg-white/80 backdrop-blur-sm border border-slate-200 hover:bg-white",
                isFavorite && "text-rose-500 hover:text-rose-600"
              )}
            >
              <Heart className={cn("h-3 w-3", isFavorite && "fill-current")} />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {isFavorite ? 'Remove from favorites' : 'Add to favorites'}
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 bg-white/80 backdrop-blur-sm border border-slate-200 hover:bg-white"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Add to page</TooltipContent>
        </Tooltip>
      </div>

      {/* Hover overlay with gradient */}
      <div className={cn(
        'absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 rounded-xl opacity-0 transition-opacity pointer-events-none',
        isHovered && 'opacity-100'
      )} />

      {/* Sparkle effect on hover */}
      <div className={cn(
        'absolute top-3 left-3 opacity-0 transition-opacity duration-300',
        isHovered && 'opacity-100'
      )}>
        <Sparkles className="h-4 w-4 text-blue-400" />
      </div>
    </div>
  )
}


