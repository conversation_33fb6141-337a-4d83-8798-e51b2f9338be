'use client'

import React, { useState } from 'react'
import { usePageBuilder } from '@/stores/use-page-builder'
import { blockRegistry } from '../blocks/registry'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  ChevronDown, 
  ChevronRight, 
  Eye, 
  EyeOff, 
  Copy, 
  Trash2,
  GripVertical,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ComponentTreeProps {
  className?: string
}

export function ComponentTree({ className }: ComponentTreeProps) {
  const {
    page,
    selectedBlockId,
    selectBlock,
    updateBlock,
    deleteBlock,
    duplicateBlock,
    moveBlock
  } = usePageBuilder()
  const [expandedBlocks, setExpandedBlocks] = useState<Set<string>>(new Set())
  const [draggedBlock, setDraggedBlock] = useState<string | null>(null)

  const toggleExpanded = (blockId: string) => {
    const newExpanded = new Set(expandedBlocks)
    if (newExpanded.has(blockId)) {
      newExpanded.delete(blockId)
    } else {
      newExpanded.add(blockId)
    }
    setExpandedBlocks(newExpanded)
  }

  const handleBlockSelect = (blockId: string) => {
    selectBlock(blockId)
  }

  const handleVisibilityToggle = (blockId: string, isVisible: boolean) => {
    updateBlock(blockId, { isVisible: !isVisible })
  }

  const handleDuplicate = (blockId: string) => {
    duplicateBlock(blockId)
  }

  const handleDelete = (blockId: string) => {
    if (confirm('Are you sure you want to delete this block?')) {
      deleteBlock(blockId)
    }
  }

  const handleDragStart = (e: React.DragEvent, blockId: string) => {
    setDraggedBlock(blockId)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, targetPosition: number) => {
    e.preventDefault()
    if (draggedBlock) {
      const draggedBlockData = page.blocks.find(b => b.id === draggedBlock)
      if (draggedBlockData && draggedBlockData.position !== targetPosition) {
        moveBlock(draggedBlock, targetPosition)
      }
      setDraggedBlock(null)
    }
  }

  const getBlockIcon = (blockType: string) => {
    const blockDefinition = blockRegistry.getBlockType(blockType)
    return blockDefinition?.icon || '📦'
  }

  const getBlockDisplayName = (blockType: string) => {
    const blockDefinition = blockRegistry.getBlockType(blockType)
    return blockDefinition?.displayName || blockType
  }

  const sortedBlocks = [...page.blocks].sort((a, b) => a.position - b.position)

  return (
    <Card className={cn('h-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Page Structure</CardTitle>
          <Button variant="ghost" size="sm" title="Add Block">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[400px]">
          <div className="p-3 space-y-1">
            {sortedBlocks.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <div className="text-4xl mb-2">📄</div>
                <p className="text-sm">No blocks added yet</p>
                <p className="text-xs">Start building your page by adding blocks</p>
              </div>
            ) : (
              sortedBlocks.map((block, index) => (
                <div
                  key={block.id}
                  className={cn(
                    'group relative',
                    draggedBlock === block.id && 'opacity-50'
                  )}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, index)}
                >
                  {/* Drop Zone Above */}
                  <div
                    className="h-1 -mb-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, index)}
                  >
                    <div className="h-full bg-primary/20 rounded" />
                  </div>

                  {/* Block Item */}
                  <div
                    className={cn(
                      'flex items-center space-x-2 p-2 rounded-md cursor-pointer transition-colors',
                      'hover:bg-muted/50',
                      selectedBlockId === block.id && 'bg-primary/10 border border-primary/20'
                    )}
                    onClick={() => handleBlockSelect(block.id)}
                    draggable
                    onDragStart={(e) => handleDragStart(e, block.id)}
                  >
                    {/* Drag Handle */}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing">
                      <GripVertical className="h-3 w-3 text-muted-foreground" />
                    </div>

                    {/* Expand/Collapse Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0"
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleExpanded(block.id)
                      }}
                    >
                      {expandedBlocks.has(block.id) ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>

                    {/* Block Icon */}
                    <span className="text-sm">{getBlockIcon(block.type)}</span>

                    {/* Block Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium truncate">
                          {getBlockDisplayName(block.type)}
                        </span>
                        {!block.isVisible && (
                          <EyeOff className="h-3 w-3 text-muted-foreground" />
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Position: {block.position + 1}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleVisibilityToggle(block.id, block.isVisible)
                        }}
                        title={block.isVisible ? 'Hide block' : 'Show block'}
                      >
                        {block.isVisible ? (
                          <Eye className="h-3 w-3" />
                        ) : (
                          <EyeOff className="h-3 w-3" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDuplicate(block.id)
                        }}
                        title="Duplicate block"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(block.id)
                        }}
                        title="Delete block"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Expanded Content */}
                  {expandedBlocks.has(block.id) && (
                    <div className="ml-8 mt-2 space-y-2 text-xs text-muted-foreground">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <span className="font-medium">ID:</span>
                          <div className="font-mono text-xs truncate">{block.id}</div>
                        </div>
                        <div>
                          <span className="font-medium">Type:</span>
                          <div>{block.type}</div>
                        </div>
                      </div>
                      
                      {/* Configuration Preview */}
                      {block.configuration && Object.keys(block.configuration).length > 0 && (
                        <div>
                          <span className="font-medium">Configuration:</span>
                          <div className="mt-1 p-2 bg-muted/30 rounded text-xs">
                            {Object.entries(block.configuration)
                              .slice(0, 3)
                              .map(([key, value]) => (
                                <div key={key} className="flex justify-between">
                                  <span className="truncate">{key}:</span>
                                  <span className="truncate ml-2 max-w-[100px]">
                                    {typeof value === 'string' ? value : JSON.stringify(value)}
                                  </span>
                                </div>
                              ))}
                            {Object.keys(block.configuration).length > 3 && (
                              <div className="text-muted-foreground">
                                +{Object.keys(block.configuration).length - 3} more...
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Responsive Settings */}
                      {block.responsive && (
                        <div>
                          <span className="font-medium">Responsive:</span>
                          <div className="flex space-x-2 mt-1">
                            {block.responsive.hideOnMobile && (
                              <span className="px-1 py-0.5 bg-red-100 text-red-700 rounded text-xs">
                                Hidden on Mobile
                              </span>
                            )}
                            {block.responsive.hideOnTablet && (
                              <span className="px-1 py-0.5 bg-red-100 text-red-700 rounded text-xs">
                                Hidden on Tablet
                              </span>
                            )}
                            {block.responsive.hideOnDesktop && (
                              <span className="px-1 py-0.5 bg-red-100 text-red-700 rounded text-xs">
                                Hidden on Desktop
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Animation Settings */}
                      {block.animation?.enabled && (
                        <div>
                          <span className="font-medium">Animation:</span>
                          <div className="mt-1">
                            <span className="px-1 py-0.5 bg-blue-100 text-blue-700 rounded text-xs">
                              {block.animation.type} - {block.animation.trigger}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Drop Zone Below (for last item) */}
                  {index === sortedBlocks.length - 1 && (
                    <div
                      className="h-1 -mt-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDrop(e, index + 1)}
                    >
                      <div className="h-full bg-primary/20 rounded" />
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
