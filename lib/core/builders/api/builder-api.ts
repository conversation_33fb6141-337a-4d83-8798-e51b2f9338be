// Builder API Layer
// Handles all API communications for the builder system

import { 
  BuilderPage, 
  BuilderTemplate, 
  BuilderComponent, 
  BuilderAPI as IBuilderAPI 
} from '../../../builders/types'

class BuilderAPIClass implements IBuilderAPI {
  private baseUrl: string
  private apiKey?: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api'
    this.apiKey = process.env.NEXT_PUBLIC_BUILDER_API_KEY
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/builder${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`Builder API Error (${endpoint}):`, error)
      throw error
    }
  }

  // Pages API
  async getPages(filters?: any): Promise<BuilderPage[]> {
    const queryParams = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }

    const endpoint = `/pages${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await this.request<{ pages: BuilderPage[] }>(endpoint)
    return response.pages
  }

  async getPage(id: string): Promise<BuilderPage | null> {
    try {
      const response = await this.request<{ page: BuilderPage }>(`/pages/${id}`)
      return response.page
    } catch (error) {
      if (error.message.includes('404')) {
        return null
      }
      throw error
    }
  }

  async createPage(page: Partial<BuilderPage>): Promise<BuilderPage> {
    const response = await this.request<{ page: BuilderPage }>('/pages', {
      method: 'POST',
      body: JSON.stringify({ page })
    })
    return response.page
  }

  async updatePage(id: string, updates: Partial<BuilderPage>): Promise<BuilderPage> {
    const response = await this.request<{ page: BuilderPage }>(`/pages/${id}`, {
      method: 'PATCH',
      body: JSON.stringify({ updates })
    })
    return response.page
  }

  async deletePage(id: string): Promise<void> {
    await this.request(`/pages/${id}`, {
      method: 'DELETE'
    })
  }

  async duplicatePage(id: string): Promise<BuilderPage> {
    const response = await this.request<{ page: BuilderPage }>(`/pages/${id}/duplicate`, {
      method: 'POST'
    })
    return response.page
  }

  async publishPage(id: string): Promise<BuilderPage> {
    const response = await this.request<{ page: BuilderPage }>(`/pages/${id}/publish`, {
      method: 'POST'
    })
    return response.page
  }

  async unpublishPage(id: string): Promise<BuilderPage> {
    const response = await this.request<{ page: BuilderPage }>(`/pages/${id}/unpublish`, {
      method: 'POST'
    })
    return response.page
  }

  // Templates API
  async getTemplates(filters?: any): Promise<BuilderTemplate[]> {
    const queryParams = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }

    const endpoint = `/templates${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await this.request<{ templates: BuilderTemplate[] }>(endpoint)
    return response.templates
  }

  async getTemplate(id: string): Promise<BuilderTemplate | null> {
    try {
      const response = await this.request<{ template: BuilderTemplate }>(`/templates/${id}`)
      return response.template
    } catch (error) {
      if (error.message.includes('404')) {
        return null
      }
      throw error
    }
  }

  async createTemplate(template: Partial<BuilderTemplate>): Promise<BuilderTemplate> {
    const response = await this.request<{ template: BuilderTemplate }>('/templates', {
      method: 'POST',
      body: JSON.stringify({ template })
    })
    return response.template
  }

  async updateTemplate(id: string, updates: Partial<BuilderTemplate>): Promise<BuilderTemplate> {
    const response = await this.request<{ template: BuilderTemplate }>(`/templates/${id}`, {
      method: 'PATCH',
      body: JSON.stringify({ updates })
    })
    return response.template
  }

  async deleteTemplate(id: string): Promise<void> {
    await this.request(`/templates/${id}`, {
      method: 'DELETE'
    })
  }

  // Components API
  async getComponents(filters?: any): Promise<BuilderComponent[]> {
    const queryParams = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }

    const endpoint = `/components${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await this.request<{ components: BuilderComponent[] }>(endpoint)
    return response.components
  }

  async getComponent(id: string): Promise<BuilderComponent | null> {
    try {
      const response = await this.request<{ component: BuilderComponent }>(`/components/${id}`)
      return response.component
    } catch (error) {
      if (error.message.includes('404')) {
        return null
      }
      throw error
    }
  }

  async createComponent(component: Partial<BuilderComponent>): Promise<BuilderComponent> {
    const response = await this.request<{ component: BuilderComponent }>('/components', {
      method: 'POST',
      body: JSON.stringify({ component })
    })
    return response.component
  }

  async updateComponent(id: string, updates: Partial<BuilderComponent>): Promise<BuilderComponent> {
    const response = await this.request<{ component: BuilderComponent }>(`/components/${id}`, {
      method: 'PATCH',
      body: JSON.stringify({ updates })
    })
    return response.component
  }

  async deleteComponent(id: string): Promise<void> {
    await this.request(`/components/${id}`, {
      method: 'DELETE'
    })
  }

  // Assets API
  async uploadAsset(file: File): Promise<{ url: string; id: string }> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${this.baseUrl}/builder/assets/upload`, {
      method: 'POST',
      body: formData,
      headers: this.apiKey ? { 'Authorization': `Bearer ${this.apiKey}` } : {}
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`)
    }

    const result = await response.json()
    return result.asset
  }

  async getAssets(filters?: any): Promise<any[]> {
    const queryParams = new URLSearchParams()
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }

    const endpoint = `/assets${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await this.request<{ assets: any[] }>(endpoint)
    return response.assets
  }

  async deleteAsset(id: string): Promise<void> {
    await this.request(`/assets/${id}`, {
      method: 'DELETE'
    })
  }

  // Utility methods
  async validatePage(page: BuilderPage): Promise<{ isValid: boolean; errors: string[] }> {
    const response = await this.request<{ isValid: boolean; errors: string[] }>('/validate/page', {
      method: 'POST',
      body: JSON.stringify({ page })
    })
    return response
  }

  async exportPage(id: string, format: 'json' | 'html' | 'react'): Promise<string> {
    const response = await this.request<{ content: string }>(`/pages/${id}/export?format=${format}`)
    return response.content
  }

  async importPage(content: string, format: 'json' | 'html'): Promise<BuilderPage> {
    const response = await this.request<{ page: BuilderPage }>('/pages/import', {
      method: 'POST',
      body: JSON.stringify({ content, format })
    })
    return response.page
  }

  async previewPage(page: BuilderPage): Promise<{ url: string }> {
    const response = await this.request<{ url: string }>('/pages/preview', {
      method: 'POST',
      body: JSON.stringify({ page })
    })
    return response
  }

  async generateSitemap(): Promise<{ url: string }> {
    const response = await this.request<{ url: string }>('/sitemap/generate', {
      method: 'POST'
    })
    return response
  }

  // Search and analytics
  async searchPages(query: string, filters?: any): Promise<BuilderPage[]> {
    const queryParams = new URLSearchParams({ q: query })
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }

    const response = await this.request<{ pages: BuilderPage[] }>(`/search/pages?${queryParams.toString()}`)
    return response.pages
  }

  async getPageAnalytics(id: string, period: '7d' | '30d' | '90d' = '30d'): Promise<any> {
    const response = await this.request<any>(`/analytics/pages/${id}?period=${period}`)
    return response
  }

  async getBuilderAnalytics(period: '7d' | '30d' | '90d' = '30d'): Promise<any> {
    const response = await this.request<any>(`/analytics/builder?period=${period}`)
    return response
  }
}

// Create singleton instance
export const BuilderAPI = new BuilderAPIClass()

// Mock implementation for development
export class MockBuilderAPI implements IBuilderAPI {
  private pages: BuilderPage[] = []
  private templates: BuilderTemplate[] = []
  private components: BuilderComponent[] = []
  private assets: any[] = []

  async getPages(filters?: any): Promise<BuilderPage[]> {
    await this.delay(100)
    return this.pages.filter(page => {
      if (filters?.status && page.status !== filters.status) return false
      if (filters?.type && page.type !== filters.type) return false
      return true
    })
  }

  async getPage(id: string): Promise<BuilderPage | null> {
    await this.delay(100)
    return this.pages.find(page => page.id === id) || null
  }

  async createPage(page: Partial<BuilderPage>): Promise<BuilderPage> {
    await this.delay(200)
    const newPage: BuilderPage = {
      id: `page_${Date.now()}`,
      title: page.title || 'Untitled Page',
      slug: page.slug || 'untitled-page',
      description: page.description || '',
      status: page.status || 'draft',
      type: page.type || 'page',
      blocks: page.blocks || [],
      settings: page.settings || {
        layout: 'boxed',
        maxWidth: 1200
      },
      seo: page.seo || {},
      performance: page.performance || {
        lazyLoading: true,
        imageOptimization: true,
        cacheStrategy: 'browser',
        preloadCritical: true,
        minifyOutput: true
      },
      version: 1,
      authorId: page.authorId || 'mock-user',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...page
    }
    this.pages.push(newPage)
    return newPage
  }

  async updatePage(id: string, updates: Partial<BuilderPage>): Promise<BuilderPage> {
    await this.delay(150)
    const pageIndex = this.pages.findIndex(page => page.id === id)
    if (pageIndex === -1) throw new Error('Page not found')
    
    this.pages[pageIndex] = {
      ...this.pages[pageIndex],
      ...updates,
      updatedAt: new Date()
    }
    return this.pages[pageIndex]
  }

  async deletePage(id: string): Promise<void> {
    await this.delay(100)
    this.pages = this.pages.filter(page => page.id !== id)
  }

  async getTemplates(filters?: any): Promise<BuilderTemplate[]> {
    await this.delay(100)
    return this.templates
  }

  async getTemplate(id: string): Promise<BuilderTemplate | null> {
    await this.delay(100)
    return this.templates.find(template => template.id === id) || null
  }

  async createTemplate(template: Partial<BuilderTemplate>): Promise<BuilderTemplate> {
    await this.delay(200)
    const newTemplate: BuilderTemplate = {
      id: `template_${Date.now()}`,
      name: template.name || 'Untitled Template',
      description: template.description || '',
      category: template.category || 'general',
      thumbnail: template.thumbnail || '',
      blocks: template.blocks || [],
      settings: template.settings || { layout: 'boxed' },
      isPublic: template.isPublic || false,
      isPremium: template.isPremium || false,
      tags: template.tags || [],
      authorId: template.authorId || 'mock-user',
      downloads: 0,
      rating: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...template
    }
    this.templates.push(newTemplate)
    return newTemplate
  }

  async getComponents(filters?: any): Promise<BuilderComponent[]> {
    await this.delay(100)
    return this.components
  }

  async getComponent(id: string): Promise<BuilderComponent | null> {
    await this.delay(100)
    return this.components.find(component => component.id === id) || null
  }

  async createComponent(component: Partial<BuilderComponent>): Promise<BuilderComponent> {
    await this.delay(200)
    const newComponent: BuilderComponent = {
      id: `component_${Date.now()}`,
      name: component.name || 'Untitled Component',
      description: component.description || '',
      category: component.category || 'general',
      icon: component.icon || 'square',
      blocks: component.blocks || [],
      props: component.props || [],
      isReusable: component.isReusable || true,
      isGlobal: component.isGlobal || false,
      version: component.version || '1.0.0',
      authorId: component.authorId || 'mock-user',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...component
    }
    this.components.push(newComponent)
    return newComponent
  }

  async uploadAsset(file: File): Promise<{ url: string; id: string }> {
    await this.delay(500)
    const asset = {
      id: `asset_${Date.now()}`,
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size,
      type: file.type,
      createdAt: new Date()
    }
    this.assets.push(asset)
    return { url: asset.url, id: asset.id }
  }

  async getAssets(filters?: any): Promise<any[]> {
    await this.delay(100)
    return this.assets
  }

  async deleteAsset(id: string): Promise<void> {
    await this.delay(100)
    this.assets = this.assets.filter(asset => asset.id !== id)
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Use mock API in development
export const API = process.env.NODE_ENV === 'development' 
  ? new MockBuilderAPI() 
  : BuilderAPI

export default BuilderAPI
