'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Database, 
  Link, 
  Plus, 
  X, 
  Settings,
  Eye,
  EyeOff,
  Info,
  Zap,
  ArrowRight,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCMSBuilder } from '../../cms-builder-context'
import { CustomFieldRenderer } from './custom-fields'
import { CustomField, CustomFieldBinding } from '@/lib/cms/types'
import { FieldConfig, FieldValue } from './custom-fields/types'

interface BlockCustomFieldsEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function BlockCustomFieldsEditor({ block, onUpdate }: BlockCustomFieldsEditorProps) {
  const { state: cmsState } = useCMSBuilder()
  const [activeTab, setActiveTab] = useState<'bindings' | 'values'>('bindings')
  const [showBindingForm, setShowBindingForm] = useState(false)
  const [newBinding, setNewBinding] = useState<Partial<CustomFieldBinding>>({})

  // Get available custom fields for the current post type
  const availableCustomFields = useMemo(() => {
    if (!cmsState.currentPostType || !cmsState.customFields) {
      return []
    }
    
    // Filter custom fields that are available for the current post type
    return cmsState.customFields.filter(field => 
      field.postTypes?.includes(cmsState.currentPostType?.name || '') ||
      field.postTypes?.includes('*') ||
      !field.postTypes?.length
    )
  }, [cmsState.currentPostType, cmsState.customFields])

  // Get current custom field bindings for this block
  const currentBindings = useMemo(() => {
    return block.customFieldBindings || []
  }, [block.customFieldBindings])

  // Get current custom field values for this block
  const currentValues = useMemo(() => {
    return block.customFieldValues || {}
  }, [block.customFieldValues])

  // Get block properties that can be bound to custom fields
  const bindableProperties = useMemo(() => {
    const config = block.configuration || {}
    const properties: string[] = []
    
    // Extract all string/text properties from block configuration
    const extractProperties = (obj: any, prefix = '') => {
      Object.keys(obj).forEach(key => {
        const value = obj[key]
        const fullKey = prefix ? `${prefix}.${key}` : key
        
        if (typeof value === 'string' || typeof value === 'number') {
          properties.push(fullKey)
        } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          extractProperties(value, fullKey)
        }
      })
    }
    
    extractProperties(config)
    return properties
  }, [block.configuration])

  // Handle adding a new custom field binding
  const handleAddBinding = () => {
    if (!newBinding.fieldId || !newBinding.blockProperty) {
      return
    }

    const field = availableCustomFields.find(f => f.id === newBinding.fieldId)
    if (!field) return

    const binding: CustomFieldBinding = {
      fieldId: newBinding.fieldId,
      fieldName: field.name,
      blockProperty: newBinding.blockProperty,
      transformFunction: newBinding.transformFunction || undefined,
      validationRules: []
    }

    const updatedBindings = [...currentBindings, binding]
    
    onUpdate({
      customFieldBindings: updatedBindings
    })

    setNewBinding({})
    setShowBindingForm(false)
  }

  // Handle removing a custom field binding
  const handleRemoveBinding = (index: number) => {
    const updatedBindings = currentBindings.filter((_: any, i: number) => i !== index)
    onUpdate({
      customFieldBindings: updatedBindings
    })
  }

  // Handle custom field value changes
  const handleCustomFieldValueChange = (fieldId: string, value: FieldValue) => {
    const updatedValues = {
      ...currentValues,
      [fieldId]: value
    }
    
    onUpdate({
      customFieldValues: updatedValues
    })

    // Also update bound block properties
    const binding = currentBindings.find((b: CustomFieldBinding) => b.fieldId === fieldId)
    if (binding) {
      updateBlockPropertyFromBinding(binding, value)
    }
  }

  // Update block property based on custom field binding
  const updateBlockPropertyFromBinding = (binding: CustomFieldBinding, value: FieldValue) => {
    const propertyPath = binding.blockProperty.split('.')
    const config = { ...block.configuration }
    
    // Navigate to the property and update it
    let current = config
    for (let i = 0; i < propertyPath.length - 1; i++) {
      if (!current[propertyPath[i]]) {
        current[propertyPath[i]] = {}
      }
      current = current[propertyPath[i]]
    }
    
    // Apply transform function if specified
    let transformedValue = value
    if (binding.transformFunction) {
      try {
        // Simple transform functions
        switch (binding.transformFunction) {
          case 'uppercase':
            transformedValue = String(value).toUpperCase()
            break
          case 'lowercase':
            transformedValue = String(value).toLowerCase()
            break
          case 'capitalize':
            transformedValue = String(value).charAt(0).toUpperCase() + String(value).slice(1)
            break
          default:
            transformedValue = value
        }
      } catch (error) {
        console.warn('Transform function failed:', error)
        transformedValue = value
      }
    }
    
    current[propertyPath[propertyPath.length - 1]] = transformedValue
    
    onUpdate({
      configuration: config
    })
  }

  // Convert custom field to field config for rendering
  const convertCustomFieldToFieldConfig = (customField: CustomField): FieldConfig => {
    return {
      id: customField.id,
      type: mapCustomFieldTypeToFieldType(customField.type),
      label: customField.label,
      description: customField.instructions,
      required: customField.required,
      defaultValue: customField.defaultValue,
      placeholder: customField.placeholder,
      options: customField.choices ? Object.entries(customField.choices).map(([value, label]) => ({
        label,
        value
      })) : undefined,
      disabled: customField.disabled,
      validation: {
        required: customField.required,
        maxLength: customField.maxLength
      }
    }
  }

  // Map CMS custom field types to our field renderer types
  const mapCustomFieldTypeToFieldType = (cmsType: string): string => {
    const typeMap: Record<string, string> = {
      'text': 'text',
      'textarea': 'textarea',
      'number': 'number',
      'email': 'text',
      'url': 'text',
      'password': 'text',
      'select': 'select',
      'checkbox': 'boolean',
      'radio': 'select',
      'date_picker': 'date',
      'date_time_picker': 'datetime',
      'time_picker': 'time',
      'color_picker': 'color',
      'file': 'file',
      'image': 'image',
      'gallery': 'media',
      'wysiwyg': 'richtext',
      'oembed': 'text',
      'google_map': 'text',
      'repeater': 'repeater',
      'flexible_content': 'object',
      'clone': 'object',
      'message': 'text',
      'accordion': 'accordion',
      'tab': 'tabs',
      'group': 'object'
    }
    
    return typeMap[cmsType] || 'text'
  }

  if (!cmsState.currentPostType) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <Database className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">No post type selected</p>
            <p className="text-xs">Custom fields are only available when editing content with a specific post type.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!availableCustomFields.length) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <Database className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm">No custom fields available</p>
            <p className="text-xs">
              No custom fields are configured for the "{cmsState.currentPostType.label}" post type.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Database className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-sm font-semibold">Custom Fields</CardTitle>
                <p className="text-xs text-muted-foreground">
                  Bind custom fields to block properties or set field values
                </p>
              </div>
            </div>
            <Badge variant="secondary" className="text-xs">
              {availableCustomFields.length} fields
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="bindings" className="text-xs">
            <Link className="h-3 w-3 mr-1" />
            Field Bindings
          </TabsTrigger>
          <TabsTrigger value="values" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Field Values
          </TabsTrigger>
        </TabsList>

        {/* Field Bindings Tab */}
        <TabsContent value="bindings" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Property Bindings</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBindingForm(!showBindingForm)}
                  className="text-xs h-7"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Binding
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Add Binding Form */}
              {showBindingForm && (
                <div className="space-y-3 p-3 bg-gray-50 rounded-lg mb-4">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-xs">Custom Field</Label>
                      <Select
                        value={newBinding.fieldId || ''}
                        onValueChange={(value) => setNewBinding({ ...newBinding, fieldId: value })}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableCustomFields.map((field) => (
                            <SelectItem key={field.id} value={field.id}>
                              {field.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="text-xs">Block Property</Label>
                      <Select
                        value={newBinding.blockProperty || ''}
                        onValueChange={(value) => setNewBinding({ ...newBinding, blockProperty: value })}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="Select property" />
                        </SelectTrigger>
                        <SelectContent>
                          {bindableProperties.map((property) => (
                            <SelectItem key={property} value={property}>
                              {property}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleAddBinding}
                      disabled={!newBinding.fieldId || !newBinding.blockProperty}
                      size="sm"
                      className="text-xs h-7"
                    >
                      Add Binding
                    </Button>
                    <Button
                      onClick={() => setShowBindingForm(false)}
                      variant="outline"
                      size="sm"
                      className="text-xs h-7"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {/* Current Bindings */}
              {currentBindings.length > 0 ? (
                <div className="space-y-2">
                  {currentBindings.map((binding: CustomFieldBinding, index: number) => {
                    const field = availableCustomFields.find(f => f.id === binding.fieldId)
                    return (
                      <div key={index} className="flex items-center justify-between p-3 bg-white border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                            <Database className="h-3 w-3 text-blue-600" />
                          </div>
                          <div>
                            <div className="text-xs font-medium">{field?.label || binding.fieldName}</div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <ArrowRight className="h-3 w-3" />
                              {binding.blockProperty}
                            </div>
                          </div>
                        </div>
                        <Button
                          onClick={() => handleRemoveBinding(index)}
                          variant="ghost"
                          size="sm"
                          className="text-xs h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <Link className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No field bindings</p>
                  <p className="text-xs">Bind custom fields to block properties to sync their values automatically.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Field Values Tab */}
        <TabsContent value="values" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Custom Field Values</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {availableCustomFields.map((field) => {
                  const fieldConfig = convertCustomFieldToFieldConfig(field)
                  const currentValue = currentValues[field.id]
                  const isBound = currentBindings.some((b: CustomFieldBinding) => b.fieldId === field.id)
                  
                  return (
                    <div key={field.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-xs font-medium text-gray-700 flex items-center gap-2">
                          {field.label}
                          {field.required && <span className="text-red-500">*</span>}
                          {isBound && (
                            <Badge variant="secondary" className="text-xs">
                              <Link className="h-2 w-2 mr-1" />
                              Bound
                            </Badge>
                          )}
                        </label>
                      </div>

                      {field.instructions && (
                        <p className="text-xs text-gray-500">{field.instructions}</p>
                      )}

                      <CustomFieldRenderer
                        config={fieldConfig}
                        value={currentValue}
                        onChange={(value) => handleCustomFieldValueChange(field.id, value)}
                        className="w-full"
                      />
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
